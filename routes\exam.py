from flask import Blueprint, request, jsonify, render_template, redirect, url_for, current_app
from flask_login import login_required, current_user
from models import Exam, ExamSession, ExamSubmission, ExamQuestion, db
from models.proctoring_event import ProctoringEvent
from models.user import User
from werkzeug.utils import secure_filename
import os
from datetime import datetime
from utils.pdf_parser import PDFQuestionParser

exam_bp = Blueprint('exam', __name__)

# Helper function to save uploaded files
def save_uploaded_file(file, upload_folder):
    if file and file.filename:
        filename = secure_filename(file.filename)
        # Add timestamp to avoid conflicts
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
        filename = timestamp + filename
        filepath = os.path.join(upload_folder, filename)

        # Create directory if it doesn't exist
        os.makedirs(upload_folder, exist_ok=True)
        file.save(filepath)
        return filename
    return None

@exam_bp.route('/create', methods=['GET'])
@login_required
def create_exam_form():
    if current_user.role != 'proctor':
        return redirect(url_for('exam.dashboard'))
    return render_template('exam/create_exam.html')

@exam_bp.route('/create', methods=['POST'])
@login_required
def create_exam():
    if current_user.role != 'proctor':
        return jsonify({'error': 'Unauthorized'}), 403

    # Get form data (not JSON since we're handling file uploads)
    title = request.form.get('title')
    duration = request.form.get('duration_minutes')
    question_count = request.form.get('question_count')
    question_types = request.form.getlist('question_types')  # Multiple values
    instructions = request.form.get('instructions')

    if not title or not duration:
        return render_template('exam/create_exam.html', error='Title and duration are required')

    try:
        duration = int(duration)
        if duration <= 0:
            return render_template('exam/create_exam.html', error='Duration must be a positive number')
    except ValueError:
        return render_template('exam/create_exam.html', error='Duration must be a valid number')

    # Handle PDF file upload
    pdf_file = request.files.get('pdf_file')
    pdf_filename = None
    if pdf_file and pdf_file.filename:
        if not pdf_file.filename.lower().endswith('.pdf'):
            return render_template('exam/create_exam.html', error='Only PDF files are allowed')

        upload_folder = os.path.join(current_app.config.get('FILE_STORAGE_PATH', './storage'), 'exam_pdfs')
        pdf_filename = save_uploaded_file(pdf_file, upload_folder)

    # Create exam
    exam = Exam(
        title=title,
        duration_minutes=duration,
        created_by_id=current_user.id,
        pdf_file_path=pdf_filename,
        question_count=int(question_count) if question_count else None,
        question_types=','.join(question_types) if question_types else None,
        instructions=instructions
    )

    db.session.add(exam)
    db.session.commit()

    # Auto-parse questions if PDF was uploaded
    if pdf_filename:
        try:
            parser = PDFQuestionParser()
            pdf_path = os.path.join('storage', 'exam_pdfs', pdf_filename)

            if os.path.exists(pdf_path):
                questions = parser.parse_pdf_questions(pdf_path)
            else:
                # Use sample questions if PDF parsing fails
                questions = parser.create_sample_questions(exam.id) # type: ignore

            # Save parsed questions to database
            for q_data in questions:
                question = ExamQuestion(
                    exam_id=exam.id,
                    question_number=q_data['question_number'],
                    question_text=q_data['question_text'],
                    question_type=q_data['question_type'],
                    options=q_data['options'],
                    points=q_data['points']
                )
                db.session.add(question)

            db.session.commit()
        except Exception as e:
            print(f"Error parsing questions: {e}")
            # Continue without questions - they can be parsed later

    return redirect(url_for('exam.dashboard'))

@exam_bp.route('/list', methods=['GET'])
@login_required
def list_exams():
    if current_user.role == 'proctor':
        exams = Exam.query.filter_by(created_by_id=current_user.id).all()
    else:
        exams = Exam.query.all()  # TODO: filter for assigned exams
    return jsonify([{'id': e.id, 'title': e.title, 'duration': e.duration_minutes} for e in exams])

@exam_bp.route('/dashboard', methods=['GET'])
@login_required
def dashboard():
    if current_user.role == 'proctor':
        exams = Exam.query.filter_by(created_by_id=current_user.id).all()
    else:
        exams = Exam.query.all()  # TODO: filter for assigned exams
    return render_template('exam/exam_list.html', exams=exams, user_role=current_user.role)

@exam_bp.route('/take/<int:exam_id>')
@login_required
def take_exam(exam_id):
    if current_user.role != 'student':
        return redirect(url_for('exam.dashboard'))

    exam = Exam.query.get_or_404(exam_id)

    # Check if student already submitted this exam
    existing_submission = ExamSubmission.query.filter_by(
        student_id=current_user.id,
        exam_id=exam_id
    ).first()

    if existing_submission:
        return render_template('exam/exam_completed.html',
                             exam=exam,
                             submission=existing_submission)

    # Check if there's already an active session for this student and exam
    active_session = ExamSession.query.filter_by(
        student_id=current_user.id,
        exam_id=exam_id,
        end_time=None
    ).first()

    # If no active session exists, create one
    if not active_session:
        active_session = ExamSession(
            student_id=current_user.id,
            exam_id=exam_id,
            start_time=datetime.now(),
            trust_score=100.0  # Start with full trust
        )
        db.session.add(active_session)
        db.session.commit()

        # Create initial proctoring event
        initial_event = ProctoringEvent(
            session_id=active_session.id,
            timestamp=datetime.now(),
            event_type='exam_started',
            severity_score=0.0,
            event_metadata={'action': 'Student started exam'}
        )
        db.session.add(initial_event)
        db.session.commit()

    # Get parsed questions
    questions = ExamQuestion.query.filter_by(exam_id=exam_id).order_by(ExamQuestion.question_number).all() # type: ignore

    return render_template('exam/take_exam.html', exam=exam, questions=questions, session=active_session)

@exam_bp.route('/view_pdf/<int:exam_id>')
@login_required
def view_pdf(exam_id):
    exam = Exam.query.get_or_404(exam_id)
    if not exam.pdf_file_path:
        return "No PDF available for this exam", 404

    # Serve the PDF file
    from flask import send_file
    import os
    pdf_path = os.path.join('storage', 'exam_pdfs', exam.pdf_file_path)
    if os.path.exists(pdf_path):
        return send_file(pdf_path, as_attachment=False, mimetype='application/pdf')
    else:
        return "PDF file not found", 404

@exam_bp.route('/details/<int:exam_id>')
@login_required
def exam_details(exam_id):
    exam = Exam.query.get_or_404(exam_id)
    # Check if user has permission to view this exam
    if current_user.role == 'proctor' and exam.created_by_id != current_user.id:
        return redirect(url_for('exam.dashboard'))

    # Calculate real-time statistics
    stats = {}
    if current_user.role == 'proctor':
        # Total submissions
        total_submissions = ExamSubmission.query.filter_by(exam_id=exam_id).count()

        # Completed submissions (all submissions are completed when submitted)
        completed_submissions = total_submissions

        # In progress (active sessions without submissions)
        active_sessions = ExamSession.query.filter_by(exam_id=exam_id, end_time=None).count()
        submitted_student_ids = [s.student_id for s in ExamSubmission.query.filter_by(exam_id=exam_id).all()]
        in_progress = ExamSession.query.filter(
            ExamSession.exam_id == exam_id, # type: ignore
            ExamSession.end_time.is_(None), # type: ignore
            ~ExamSession.student_id.in_(submitted_student_ids) # type: ignore
        ).count() if submitted_student_ids else active_sessions

        # Average score
        submissions_with_scores = ExamSubmission.query.filter(
            ExamSubmission.exam_id == exam_id, # type: ignore
            ExamSubmission.score.isnot(None),
            ExamSubmission.max_score.isnot(None),
            ExamSubmission.max_score > 0
        ).all()

        if submissions_with_scores:
            avg_percentage = sum((s.score / s.max_score * 100) for s in submissions_with_scores) / len(submissions_with_scores)
            avg_score = f"{avg_percentage:.1f}%"
        else:
            avg_score = "N/A"

        stats = {
            'total_attempts': total_submissions,
            'completed': completed_submissions,
            'in_progress': in_progress,
            'average_score': avg_score
        }

    return render_template('exam/exam_details.html', exam=exam, stats=stats)

@exam_bp.route('/monitor/<int:exam_id>')
@login_required
def monitor_exam(exam_id):
    return f"<h1>Monitoring Exam ID: {exam_id}</h1><p>Basic monitoring page working!</p>"

@exam_bp.route('/parse_questions/<int:exam_id>', methods=['POST'])
@login_required
def parse_questions(exam_id):
    """Parse questions from PDF and store them in database"""
    if current_user.role != 'proctor':
        return jsonify({'error': 'Unauthorized'}), 403

    exam = Exam.query.get_or_404(exam_id)
    if exam.created_by_id != current_user.id:
        return jsonify({'error': 'Unauthorized'}), 403

    if not exam.pdf_file_path:
        return jsonify({'error': 'No PDF file found for this exam'}), 400

    # Parse questions from PDF
    parser = PDFQuestionParser()
    pdf_path = os.path.join('storage', 'exam_pdfs', exam.pdf_file_path)

    try:
        if os.path.exists(pdf_path):
            questions = parser.parse_pdf_questions(pdf_path)
        else:
            # Use sample questions if PDF not found
            questions = parser.create_sample_questions(exam_id) # type: ignore

        # Clear existing questions for this exam
        ExamQuestion.query.filter_by(exam_id=exam_id).delete()

        # Save parsed questions to database
        for q_data in questions:
            question = ExamQuestion(
                exam_id=exam_id,
                question_number=q_data['question_number'],
                question_text=q_data['question_text'],
                question_type=q_data['question_type'],
                options=q_data['options'],
                points=q_data['points']
            )
            db.session.add(question)

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'Successfully parsed {len(questions)} questions',
            'question_count': len(questions)
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'Failed to parse questions: {str(e)}'}), 500

@exam_bp.route('/questions/<int:exam_id>')
@login_required
def get_exam_questions(exam_id):
    """Get parsed questions for an exam"""
    exam = Exam.query.get_or_404(exam_id)
    questions = ExamQuestion.query.filter_by(exam_id=exam_id).order_by(ExamQuestion.question_number).all() # type: ignore

    questions_data = []
    for q in questions:
        questions_data.append({
            'id': q.id,
            'question_number': q.question_number,
            'question_text': q.question_text,
            'question_type': q.question_type,
            'options': q.options,
            'points': q.points
        })

    return jsonify({
        'exam': {
            'id': exam.id,
            'title': exam.title,
            'duration_minutes': exam.duration_minutes
        },
        'questions': questions_data
    })

@exam_bp.route('/submit/<int:exam_id>', methods=['POST'])
@login_required
def submit_exam(exam_id):
    """Submit exam answers"""
    if current_user.role != 'student':
        return jsonify({'error': 'Only students can submit exams'}), 403

    exam = Exam.query.get_or_404(exam_id)
    data = request.get_json()

    if not data or 'answers' not in data:
        return jsonify({'error': 'No answers provided'}), 400

    # Check if student already submitted this exam
    existing_submission = ExamSubmission.query.filter_by(
        student_id=current_user.id,
        exam_id=exam_id
    ).first()

    if existing_submission:
        return jsonify({'error': 'You have already submitted this exam'}), 400

    # Create submission
    submission = ExamSubmission(
        student_id=current_user.id,
        exam_id=exam_id,
        answers=data['answers'],
        time_taken_minutes=data.get('time_taken_minutes')
    )

    # Auto-grade if possible
    questions = ExamQuestion.query.filter_by(exam_id=exam_id).all()
    score = 0
    max_score = 0

    for question in questions:
        max_score += question.points
        question_id = str(question.question_number)
        if question_id in data['answers'] and question.correct_answer:
            if data['answers'][question_id] == question.correct_answer:
                score += question.points

    submission.score = score # type: ignore
    submission.max_score = max_score # type: ignore

    db.session.add(submission)

    # End the exam session
    active_session = ExamSession.query.filter_by(
        student_id=current_user.id,
        exam_id=exam_id,
        end_time=None
    ).first()

    if active_session:
        active_session.end_time = datetime.now()

        # Create final proctoring event
        final_event = ProctoringEvent(
            session_id=active_session.id,
            timestamp=datetime.now(),
            event_type='exam_submitted',
            severity_score=0.0,
            event_metadata={
                'action': 'Student submitted exam',
                'score': score,
                'max_score': max_score,
                'percentage': round((score / max_score * 100) if max_score > 0 else 0, 2)
            }
        )
        db.session.add(final_event)

    db.session.commit()

    return jsonify({
        'success': True,
        'message': 'Exam submitted successfully',
        'score': score,
        'max_score': max_score,
        'percentage': round((score / max_score * 100) if max_score > 0 else 0, 2)
    })

@exam_bp.route('/edit/<int:exam_id>', methods=['GET', 'POST'])
@login_required
def edit_exam(exam_id):
    """Edit an existing exam"""
    if current_user.role != 'proctor':
        return redirect(url_for('exam.dashboard'))

    exam = Exam.query.get_or_404(exam_id)
    if exam.created_by_id != current_user.id:
        return redirect(url_for('exam.dashboard'))

    if request.method == 'POST':
        # Update exam details
        exam.title = request.form.get('title', exam.title)
        exam.duration_minutes = int(request.form.get('duration', exam.duration_minutes))
        exam.instructions = request.form.get('instructions', exam.instructions)

        # Handle question types
        question_types = request.form.getlist('question_types')
        if question_types:
            exam.question_types = ','.join(question_types)

        # Handle question count
        question_count = request.form.get('question_count')
        if question_count:
            exam.question_count = int(question_count)

        db.session.commit()
        return redirect(url_for('exam.exam_details', exam_id=exam_id))

    return render_template('exam/edit_exam.html', exam=exam)

@exam_bp.route('/delete/<int:exam_id>', methods=['POST'])
@login_required
def delete_exam(exam_id):
    """Delete an exam and all related data"""
    if current_user.role != 'proctor':
        return jsonify({'error': 'Unauthorized'}), 403

    exam = Exam.query.get_or_404(exam_id)
    if exam.created_by_id != current_user.id:
        return jsonify({'error': 'Unauthorized'}), 403

    try:
        # Delete related data in correct order
        # Delete exam submissions
        ExamSubmission.query.filter_by(exam_id=exam_id).delete()

        # Delete exam questions
        ExamQuestion.query.filter_by(exam_id=exam_id).delete()

        # Delete exam sessions and related proctoring events
        sessions = ExamSession.query.filter_by(exam_id=exam_id).all()
        for session in sessions:
            # Delete proctoring events for this session
            from models.proctoring_event import ProctoringEvent
            ProctoringEvent.query.filter_by(session_id=session.id).delete()

        # Delete exam sessions
        ExamSession.query.filter_by(exam_id=exam_id).delete()

        # Delete the exam itself
        db.session.delete(exam)
        db.session.commit()

        return jsonify({'success': True, 'message': 'Exam deleted successfully'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'Failed to delete exam: {str(e)}'}), 500

@exam_bp.route('/stats/<int:exam_id>')
@login_required
def exam_stats(exam_id):
    """Get real-time exam statistics as JSON"""
    if current_user.role != 'proctor':
        return jsonify({'error': 'Unauthorized'}), 403

    exam = Exam.query.get_or_404(exam_id)
    if exam.created_by_id != current_user.id:
        return jsonify({'error': 'Unauthorized'}), 403

    # Calculate real-time statistics
    total_submissions = ExamSubmission.query.filter_by(exam_id=exam_id).count()
    completed_submissions = total_submissions

    # In progress (active sessions without submissions)
    active_sessions = ExamSession.query.filter_by(exam_id=exam_id, end_time=None).count()
    submitted_student_ids = [s.student_id for s in ExamSubmission.query.filter_by(exam_id=exam_id).all()]
    in_progress = ExamSession.query.filter(
        ExamSession.exam_id == exam_id, # type: ignore
        ExamSession.end_time == None,
        ~ExamSession.student_id.in_(submitted_student_ids) # type: ignore
    ).count() if submitted_student_ids else active_sessions

    # Average score
    submissions_with_scores = ExamSubmission.query.filter(
        ExamSubmission.exam_id == exam_id, # type: ignore
        ExamSubmission.score.isnot(None),
        ExamSubmission.max_score.isnot(None),
        ExamSubmission.max_score > 0
    ).all()

    if submissions_with_scores:
        avg_percentage = sum((s.score / s.max_score * 100) for s in submissions_with_scores) / len(submissions_with_scores)
        avg_score = f"{avg_percentage:.1f}%"
    else:
        avg_score = "N/A"

    return jsonify({
        'total_attempts': total_submissions,
        'completed': completed_submissions,
        'in_progress': in_progress,
        'average_score': avg_score
    })

@exam_bp.route('/monitor/data/<int:exam_id>')
@login_required
def monitor_data(exam_id):
    """Get real-time monitoring data as JSON"""
    if current_user.role != 'proctor':
        return jsonify({'error': 'Unauthorized'}), 403

    exam = Exam.query.get_or_404(exam_id)
    if exam.created_by_id != current_user.id:
        return jsonify({'error': 'Unauthorized'}), 403

    # Get active sessions with student information
    active_sessions = db.session.query(ExamSession, User).join(
        User, ExamSession.student_id == User.id # type: ignore
    ).filter(
        ExamSession.exam_id == exam_id, # type: ignore
        ExamSession.end_time == None
    ).all()

    # Get recent proctoring events (last 1 hour)
    from datetime import datetime, timedelta
    one_hour_ago = datetime.now() - timedelta(hours=1)

    recent_events = []
    if active_sessions:
        session_ids = [session.id for session, user in active_sessions]
        recent_events = ProctoringEvent.query.filter(
            ProctoringEvent.session_id.in_(session_ids), # type: ignore
            ProctoringEvent.timestamp >= one_hour_ago # type: ignore
        ).order_by(ProctoringEvent.timestamp.desc()).limit(50).all() # type: ignore

    # Format active sessions data
    sessions_data = []
    for session, user in active_sessions:
        # Get recent events for this session
        session_events = [e for e in recent_events if e.session_id == session.id]

        # Calculate status indicators
        has_camera = any(e.event_type == 'camera_active' for e in session_events[-5:])
        has_audio = any(e.event_type == 'audio_active' for e in session_events[-5:])

        # Get latest violations
        violations = [e for e in session_events if e.severity_score and e.severity_score > 0.8]
        warnings = [e for e in session_events if e.severity_score and 0.5 < e.severity_score <= 0.8]

        sessions_data.append({
            'session_id': session.id,
            'student_id': user.id,
            'student_name': user.username,
            'start_time': session.start_time.strftime('%H:%M:%S') if session.start_time else 'Unknown',
            'duration': str(datetime.now() - session.start_time).split('.')[0] if session.start_time else '0:00:00',
            'trust_score': session.trust_score or 100,
            'has_camera': has_camera,
            'has_audio': has_audio,
            'violations_count': len(violations),
            'warnings_count': len(warnings),
            'latest_event': session_events[0].event_type if session_events else None
        })

    # Format recent events data
    events_data = []
    for event in recent_events[:10]:  # Last 10 events
        # Get student name for this event
        session = next((s for s, u in active_sessions if s.id == event.session_id), None)
        student_name = next((u.username for s, u in active_sessions if s.id == event.session_id), 'Unknown')

        events_data.append({
            'id': event.id,
            'student_name': student_name,
            'event_type': event.event_type,
            'timestamp': event.timestamp.strftime('%H:%M:%S'),
            'severity_score': event.severity_score or 0,
            'metadata': event.event_metadata or {}
        })

    # Calculate statistics
    total_participants = ExamSubmission.query.filter_by(exam_id=exam_id).count() + len(active_sessions)
    flagged_activities = len([e for e in recent_events if e.severity_score and e.severity_score > 0.5])
    violations = len([e for e in recent_events if e.severity_score and e.severity_score > 0.8])

    return jsonify({
        'active_sessions': sessions_data,
        'recent_events': events_data,
        'stats': {
            'active_sessions': len(active_sessions),
            'flagged_activities': flagged_activities,
            'violations': violations,
            'total_participants': total_participants
        }
    })

@exam_bp.route('/monitor/student/<int:session_id>')
@login_required
def monitor_student(session_id):
    """Get detailed view of a specific student's session"""
    if current_user.role != 'proctor':
        return jsonify({'error': 'Unauthorized'}), 403

    # Get session with student and exam info
    session_data = db.session.query(ExamSession, User, Exam).join(
        User, ExamSession.student_id == User.id # type: ignore
    ).join(
        Exam, ExamSession.exam_id == Exam.id # type: ignore
    ).filter(ExamSession.id == session_id).first()

    if not session_data:
        return jsonify({'error': 'Session not found'}), 404

    session, user, exam = session_data

    # Check if proctor owns this exam
    if exam.created_by_id != current_user.id:
        return jsonify({'error': 'Unauthorized'}), 403

    # Get all proctoring events for this session
    events = ProctoringEvent.query.filter_by(session_id=session_id).order_by(
        ProctoringEvent.timestamp.desc() # type: ignore
    ).all()

    # Format events data
    events_data = []
    for event in events:
        events_data.append({
            'id': event.id,
            'timestamp': event.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
            'event_type': event.event_type,
            'severity_score': event.severity_score or 0,
            'metadata': event.event_metadata or {}
        })

    # Calculate session statistics
    violations = [e for e in events if e.severity_score and e.severity_score > 0.8]
    warnings = [e for e in events if e.severity_score and 0.5 < e.severity_score <= 0.8]

    session_info = {
        'session_id': session.id,
        'student_name': user.username,
        'student_email': user.email,
        'exam_title': exam.title,
        'start_time': session.start_time.strftime('%Y-%m-%d %H:%M:%S') if session.start_time else 'Unknown',
        'duration': str(datetime.now() - session.start_time).split('.')[0] if session.start_time else '0:00:00',
        'trust_score': session.trust_score or 100,
        'violations_count': len(violations),
        'warnings_count': len(warnings),
        'total_events': len(events),
        'video_archive': session.video_archive,
        'audio_archive': session.audio_archive,
        'screen_archive': session.screen_archive
    }

    return jsonify({
        'session': session_info,
        'events': events_data
    })

@exam_bp.route('/proctoring/event', methods=['POST'])
@login_required
def create_proctoring_event():
    """Create a proctoring event during exam"""
    if current_user.role != 'student':
        return jsonify({'error': 'Only students can create proctoring events'}), 403

    data = request.get_json()
    if not data:
        return jsonify({'error': 'No data provided'}), 400

    # Get the active session for this student
    active_session = ExamSession.query.filter_by(
        student_id=current_user.id,
        end_time=None
    ).first()

    if not active_session:
        return jsonify({'error': 'No active exam session found'}), 404

    # Create the proctoring event
    event = ProctoringEvent(
        session_id=active_session.id,
        timestamp=datetime.now(),
        event_type=data.get('event_type', 'unknown'),
        severity_score=data.get('severity_score', 0.0),
        event_metadata=data.get('metadata', {})
    )

    db.session.add(event)

    # Update trust score based on severity
    severity = data.get('severity_score', 0.0)
    if severity and severity > 0.5:
        # Reduce trust score for suspicious activities
        reduction = min(severity * 10, 20)  # Max 20 point reduction
        current_trust = active_session.trust_score or 100
        active_session.trust_score = max(0, current_trust - reduction)

    db.session.commit()

    return jsonify({
        'success': True,
        'event_id': event.id,
        'trust_score': active_session.trust_score
    })

@exam_bp.route('/test-monitoring')
@login_required
def test_monitoring():
    """Test route to verify monitoring endpoints work"""
    return jsonify({'message': 'Monitoring endpoints are working!', 'user': current_user.username})
