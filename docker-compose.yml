version: '3.8'
services:
  backend:
    build: .
    command: python app.py
    volumes:
      - .:/app
    ports:
      - "5000:5000"
    environment:
      - DATABASE_URL=**************************************/ai_examiner
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
  db:
    image: postgres:14
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: ai_examiner
    ports:
      - "5432:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data
  redis:
    image: redis:7
    restart: always
    ports:
      - "6379:6379"
volumes:
  pgdata:
