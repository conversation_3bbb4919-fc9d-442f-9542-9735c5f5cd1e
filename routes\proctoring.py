from flask import Blueprint, request, jsonify, send_file, abort, current_app, send_from_directory
from flask_login import login_required, current_user
from models import ExamSession, ProctoringEvent, Evidence, db
from datetime import timezone
import os

proctoring_bp = Blueprint('proctoring', __name__)

@proctoring_bp.route('/active_sessions', methods=['GET'])
@login_required
def active_sessions():
    sessions = ExamSession.query.filter(ExamSession.end_time == None).all()
    return jsonify([{'id': s.id, 'student_id': s.student_id, 'exam_id': s.exam_id} for s in sessions])


from datetime import datetime, timezone

@proctoring_bp.route('/session/start', methods=['POST'])
@login_required
def start_session():
    data = request.get_json(silent=True) or {}
    exam_id = data.get('exam_id')
    if current_user.role != 'student' or not exam_id:
        return jsonify({'error':'Invalid request'}), 400
    sess = ExamSession(student_id=current_user.id, exam_id=exam_id, start_time=datetime.now(timezone.utc))
    db.session.add(sess)
    db.session.commit()
    return jsonify({'session_id': sess.id})

@proctoring_bp.route('/session/<int:session_id>', methods=['GET'])
@login_required
def get_session(session_id):
    session = ExamSession.query.get_or_404(session_id)
    if current_user.role == 'student' and session.student_id != current_user.id:
        abort(403)
    return jsonify({'id': session.id, 'student_id': session.student_id, 'exam_id': session.exam_id})

@proctoring_bp.route('/events/<int:session_id>', methods=['GET'])
@login_required
def get_events(session_id):
    session = ExamSession.query.get_or_404(session_id)
    if current_user.role == 'student' and session.student_id != current_user.id:
        abort(403)
    events = ProctoringEvent.query.filter_by(session_id=session_id).all()
    return jsonify([
      {
        'id': e.id,
        'timestamp': e.timestamp.replace(tzinfo=timezone.utc).isoformat() if e.timestamp and e.timestamp.tzinfo is None else (e.timestamp.isoformat() if e.timestamp else None),
        'event_type': e.event_type,
        'severity': e.severity_score,
        'metadata': e.event_metadata
      } for e in events
    ])

@proctoring_bp.route('/evidence/<int:evidence_id>', methods=['GET'])
@login_required
def download_evidence(evidence_id):
    evidence = Evidence.query.get_or_404(evidence_id)
    event = ProctoringEvent.query.get_or_404(evidence.event_id)
    session = ExamSession.query.get_or_404(event.session_id)
    if current_user.role == 'student' and session.student_id != current_user.id:
        abort(403)
    # Secure file serving
    root = current_app.config['FILE_STORAGE_PATH']
    rel = evidence.file_path  # ensure this is relative
    full = os.path.realpath(os.path.join(root, rel))
    if not full.startswith(os.path.realpath(root)):
        abort(403)
    filename = os.path.basename(full)
    return send_from_directory(root, filename, as_attachment=True)
