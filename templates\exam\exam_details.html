{% extends 'base.html' %}
{% block title %}Exam Details: {{ exam.title }} | AI Examiner{% endblock %}
{% block content %}
<div style="max-width: 1000px; margin: 0 auto;">
  <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
    <h2>Exam Details</h2>
    <a href="/api/exam/dashboard" style="background: #6c757d; border: none; color: white; padding: 0.5rem 1rem; border-radius: 4px; text-decoration: none;">
      ← Back to Dashboard
    </a>
  </div>

  <div style="background: white; border: 1px solid #dee2e6; border-radius: 8px; padding: 2rem; margin-bottom: 2rem;">
    <h3 style="margin-top: 0; color: #1a2233;">{{ exam.title }}</h3>
    
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem; margin: 2rem 0;">
      <div>
        <h4 style="color: #495057; margin-bottom: 1rem;">📋 Basic Information</h4>
        <div style="background: #f8f9fa; padding: 1rem; border-radius: 4px;">
          <p><strong>Exam ID:</strong> #{{ exam.id }}</p>
          <p><strong>Duration:</strong> {{ exam.duration_minutes }} minutes</p>
          <p><strong>Created:</strong> {{ exam.created_at.strftime('%Y-%m-%d %H:%M') if exam.created_at else 'N/A' }}</p>
          <p><strong>Status:</strong> <span style="color: #28a745;">Active</span></p>
        </div>
      </div>

      <div>
        <h4 style="color: #495057; margin-bottom: 1rem;">❓ Question Details</h4>
        <div style="background: #f8f9fa; padding: 1rem; border-radius: 4px;">
          <p><strong>Total Questions:</strong> {{ exam.question_count or 'Not specified' }}</p>
          <p><strong>Question Types:</strong> {{ exam.question_types or 'Mixed' }}</p>
          <p><strong>PDF Available:</strong> 
            {% if exam.pdf_file_path %}
              <span style="color: #28a745;">✓ Yes</span>
            {% else %}
              <span style="color: #dc3545;">✗ No</span>
            {% endif %}
          </p>
        </div>
      </div>
    </div>

    {% if exam.instructions %}
    <div style="margin: 2rem 0;">
      <h4 style="color: #495057; margin-bottom: 1rem;">📝 Instructions</h4>
      <div style="background: #e7f3ff; border: 1px solid #b3d9ff; border-radius: 4px; padding: 1rem;">
        {{ exam.instructions }}
      </div>
    </div>
    {% endif %}

    {% if exam.pdf_file_path %}
    <div style="margin: 2rem 0;">
      <h4 style="color: #495057; margin-bottom: 1rem;">📄 Question Paper</h4>
      <div style="border: 1px solid #ddd; border-radius: 4px; height: 500px; background: #f8f9fa;">
        <iframe 
          src="/api/exam/view_pdf/{{ exam.id }}" 
          style="width: 100%; height: 100%; border: none; border-radius: 4px;"
          title="Exam Questions PDF">
        </iframe>
      </div>

    </div>
    {% endif %}

    <div style="margin: 2rem 0;">
      <h4 style="color: #495057; margin-bottom: 1rem;">⚙️ Actions</h4>
      <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
        {% if current_user.role == 'student' %}
          <a href="/api/exam/take/{{ exam.id }}" style="background: #28a745; border: none; color: white; padding: 0.75rem 1.5rem; border-radius: 4px; text-decoration: none; display: inline-block;">
            🚀 Take Exam
          </a>
        {% else %}
          <a href="/api/exam/monitor/{{ exam.id }}" style="background: #17a2b8; border: none; color: white; padding: 0.75rem 1.5rem; border-radius: 4px; text-decoration: none; display: inline-block;">
            📊 Monitor Sessions
          </a>
          {% if exam.pdf_file_path %}
          <button onclick="parseQuestions()" style="background: #6f42c1; border: none; color: white; padding: 0.75rem 1.5rem; border-radius: 4px; cursor: pointer;">
            🔍 Parse Questions from PDF
          </button>
          {% endif %}
          <button onclick="editExam()" style="background: #ffc107; border: none; color: #212529; padding: 0.75rem 1.5rem; border-radius: 4px; cursor: pointer;">
            ✏️ Edit Exam
          </button>
          <button onclick="deleteExam()" style="background: #dc3545; border: none; color: white; padding: 0.75rem 1.5rem; border-radius: 4px; cursor: pointer;">
            🗑️ Delete Exam
          </button>
        {% endif %}
      </div>
    </div>
  </div>

  {% if current_user.role == 'proctor' %}
  <div class="exam-statistics" style="background: white; border: 1px solid #dee2e6; border-radius: 8px; padding: 2rem;">
    <h4 style="margin-top: 0; color: #495057;">📈 Exam Statistics</h4>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
      <div data-stat="total_attempts" style="background: #f8f9fa; padding: 1rem; border-radius: 4px; text-align: center;">
        <div class="stat-value" style="font-size: 2rem; font-weight: bold; color: #007bff;">{{ stats.total_attempts if stats else 0 }}</div>
        <div style="color: #6c757d;">Total Attempts</div>
      </div>
      <div data-stat="completed" style="background: #f8f9fa; padding: 1rem; border-radius: 4px; text-align: center;">
        <div class="stat-value" style="font-size: 2rem; font-weight: bold; color: #28a745;">{{ stats.completed if stats else 0 }}</div>
        <div style="color: #6c757d;">Completed</div>
      </div>
      <div data-stat="in_progress" style="background: #f8f9fa; padding: 1rem; border-radius: 4px; text-align: center;">
        <div class="stat-value" style="font-size: 2rem; font-weight: bold; color: #ffc107;">{{ stats.in_progress if stats else 0 }}</div>
        <div style="color: #6c757d;">In Progress</div>
      </div>
      <div data-stat="average_score" style="background: #f8f9fa; padding: 1rem; border-radius: 4px; text-align: center;">
        <div class="stat-value" style="font-size: 2rem; font-weight: bold; color: #6c757d;">{{ stats.average_score if stats else 'N/A' }}</div>
        <div style="color: #6c757d;">Average Score</div>
      </div>
    </div>
  </div>
  {% endif %}
</div>

<script>
function parseQuestions() {
  if (confirm('This will parse questions from the PDF and make them available for students to answer. Continue?')) {
    // Show loading state
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '⏳ Parsing...';
    button.disabled = true;

    fetch(`/api/exam/parse_questions/{{ exam.id }}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        alert(`Success! Parsed ${data.question_count} questions from the PDF.`);
        location.reload(); // Refresh to show updated info
      } else {
        alert('Error parsing questions: ' + data.error);
      }
    })
    .catch(error => {
      console.error('Error:', error);
      alert('Error parsing questions. Please try again.');
    })
    .finally(() => {
      button.innerHTML = originalText;
      button.disabled = false;
    });
  }
}

function editExam() {
  // Redirect to edit page
  window.location.href = `/api/exam/edit/{{ exam.id }}`;
}

function deleteExam() {
  if (confirm('Are you sure you want to delete this exam? This action cannot be undone.\n\nThis will delete:\n- The exam and all its questions\n- All student submissions\n- All exam sessions and proctoring data')) {
    // Show loading state
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '⏳ Deleting...';
    button.disabled = true;

    fetch(`/api/exam/delete/{{ exam.id }}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        alert('Exam deleted successfully!');
        window.location.href = '/api/exam/dashboard';
      } else {
        alert('Error deleting exam: ' + data.error);
        button.innerHTML = originalText;
        button.disabled = false;
      }
    })
    .catch(error => {
      console.error('Error:', error);
      alert('Error deleting exam. Please try again.');
      button.innerHTML = originalText;
      button.disabled = false;
    });
  }
}

// Auto-refresh statistics every 30 seconds
setInterval(function() {
  if (document.hidden) return; // Don't refresh if tab is not active

  fetch(`/api/exam/stats/{{ exam.id }}`)
    .then(response => response.json())
    .then(stats => {
      // Update the statistics display
      const statsContainer = document.querySelector('.exam-statistics');
      if (statsContainer) {
        const statBoxes = statsContainer.querySelectorAll('[data-stat]');
        statBoxes.forEach(box => {
          const statType = box.getAttribute('data-stat');
          const valueElement = box.querySelector('.stat-value');
          if (valueElement && stats[statType] !== undefined) {
            valueElement.textContent = stats[statType];
          }
        });
      }
    })
    .catch(error => {
      console.error('Error refreshing statistics:', error);
    });
}, 30000); // Refresh every 30 seconds
</script>
{% endblock %}
