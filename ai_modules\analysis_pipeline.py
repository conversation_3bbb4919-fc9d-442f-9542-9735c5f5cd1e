from .face_recognition import generate_face_encoding, compare_faces, liveness_detection
from .gaze_tracking import estimate_head_pose, infer_gaze_direction
from .object_detection import detect_objects
from .audio_analysis import detect_speech
import threading
from flask_socketio import SocketIO
from flask import current_app
from models import ProctoringEvent, db
from datetime import datetime, timezone
import itertools

# Expose socketio instance from app
try:
    from app import socketio
except ImportError:
    socketio = None

# Main AI pipeline orchestrator
# Accepts async iterators for video_frames and audio_chunks
# Consumes a few frames/chunks and emits a placeholder event for MVP

def process_streams(session_id, video_frames, audio_chunks):
    # Consume a few frames/chunks from each iterator
    video_count = 0
    audio_count = 0
    try:
        # Consume up to 5 video frames
        for _ in range(5):
            frame = next(video_frames)
            video_count += 1
        # Consume up to 5 audio chunks
        for _ in range(5):
            chunk = next(audio_chunks)
            audio_count += 1
    except Exception:
        pass
    # Emit a placeholder event
    event_type = 'mvp_event'
    severity = 0.5
    timestamp = datetime.now(timezone.utc)
    event = ProctoringEvent(session_id=session_id, timestamp=timestamp, event_type=event_type, severity_score=severity, event_metadata={'video_frames': video_count, 'audio_chunks': audio_count})
    db.session.add(event)
    db.session.commit()
    if socketio:
        socketio.emit('proctor_alert', {
            'session_id': session_id,
            'event_type': event_type,
            'severity': severity,
            'timestamp': timestamp.isoformat()
        })
    return []
