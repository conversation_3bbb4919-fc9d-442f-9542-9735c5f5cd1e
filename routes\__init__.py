# routes/__init__.py
from .auth import auth_bp
from .exam import exam_bp
from .proctoring import proctoring_bp
from .webrtc import webrtc_bp

def register_blueprints(app):
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(exam_bp, url_prefix='/api/exam')
    app.register_blueprint(proctoring_bp, url_prefix='/api/proctoring')
    app.register_blueprint(webrtc_bp, url_prefix='/webrtc')
