{% extends 'base.html' %}
{% block title %}Dashboard | AI Examiner{% endblock %}
{% block content %}
<div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
  <h2>
    {% if current_user.role == 'proctor' %}
      My Exams
    {% else %}
      Available Exams
    {% endif %}
  </h2>
  {% if current_user.role == 'proctor' %}
    <a href="/api/exam/create" style="background: #28a745; border: none; color: white; padding: 0.5rem 1rem; border-radius: 4px; text-decoration: none; display: inline-block;">
      + Create New Exam
    </a>
  {% endif %}
</div>

{% if exams %}
  <div class="grid">
    {% for exam in exams %}
      <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 1.5rem; margin-bottom: 1rem;">
        <h3 style="margin-top: 0; color: #1a2233;">{{ exam.title }}</h3>
        <p style="color: #666; margin: 0.5rem 0;">
          <strong>Duration:</strong> {{ exam.duration_minutes }} minutes
        </p>
        <p style="color: #666; margin: 0.5rem 0;">
          <strong>Exam ID:</strong> #{{ exam.id }}
        </p>
        {% if exam.question_count %}
        <p style="color: #666; margin: 0.5rem 0;">
          <strong>Questions:</strong> {{ exam.question_count }}
        </p>
        {% endif %}
        {% if exam.pdf_file_path %}
        <p style="color: #666; margin: 0.5rem 0;">
          <strong>PDF:</strong> ✓ Uploaded
        </p>
        {% endif %}
        {% if exam.created_at %}
        <p style="color: #666; margin: 0.5rem 0;">
          <strong>Created:</strong> {{ exam.created_at.strftime('%Y-%m-%d %H:%M') }}
        </p>
        {% endif %}
        <div style="margin-top: 1rem;">
          {% if current_user.role == 'student' %}
            <a href="/api/exam/take/{{ exam.id }}" style="background: #007bff; border: none; color: white; padding: 0.5rem 1rem; border-radius: 4px; text-decoration: none; display: inline-block; margin-right: 0.5rem;">
              Take Exam
            </a>
          {% else %}
            <button onclick="viewExam('{{ exam.id }}')" style="background: #6c757d; border: none; color: white; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer; margin-right: 0.5rem;">
              View Details
            </button>
            <button onclick="monitorExam('{{ exam.id }}')" style="background: #17a2b8; border: none; color: white; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer; margin-right: 0.5rem;">
              Monitor Sessions
            </button>
          {% endif %}
        </div>
      </div>
    {% endfor %}
  </div>
{% else %}
  <div style="text-align: center; padding: 3rem; background: #f8f9fa; border-radius: 8px; color: #666;">
    <h3>No exams available</h3>
    {% if current_user.role == 'proctor' %}
      <p>Create your first exam to get started.</p>
      <a href="/api/exam/create" style="background: #28a745; border: none; color: white; padding: 0.75rem 1.5rem; border-radius: 4px; text-decoration: none; display: inline-block; font-size: 1rem;">
        Create New Exam
      </a>
    {% else %}
      <p>No exams have been assigned to you yet.</p>
    {% endif %}
  </div>
{% endif %}

<script>
function startExam(examId) {
  if (confirm('Are you ready to start the exam? Make sure you have a stable internet connection and your camera is working.')) {
    window.location.href = '/api/exam/take/' + examId;
  }
}

function createExam() {
  window.location.href = '/api/exam/create';
}

function viewExam(examId) {
  // Redirect to exam details page
  window.location.href = '/api/exam/details/' + examId;
}

function monitorExam(examId) {
  // Redirect to exam monitoring page
  window.location.href = '/api/exam/monitor/' + examId;
}
</script>
{% endblock %}
