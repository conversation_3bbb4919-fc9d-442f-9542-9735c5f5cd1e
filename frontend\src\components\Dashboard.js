import React, { useEffect, useState } from 'react';
import { getActiveSessions } from '../services/api';
import { connectWebSocket } from '../services/websocket';

function Dashboard() {
  const [sessions, setSessions] = useState([]);
  const [alerts, setAlerts] = useState([]);

  useEffect(() => {
    getActiveSessions().then(setSessions);
    const socket = connectWebSocket((alert) => setAlerts((a) => [...a, alert]));
    return () => socket.disconnect();
  }, []);

  return (
    <div>
      <h2>Live Exam Sessions</h2>
      <div className="grid">
        {sessions.map((s) => (
          <div key={s.id} className="card">
            <video src={s.videoUrl} controls width={200} />
            <div>Student: {s.studentName}</div>
            <div>Status: {s.status}</div>
          </div>
        ))}
      </div>
      <h3>Real-time Alerts</h3>
      <div>
        {alerts.map((a, i) => (
          <div key={i} className="alert">{a.message}</div>
        ))}
      </div>
    </div>
  );
}
export default Dashboard;
