// exam_client.js - Handles WebRTC, media capture, browser lockdown

let localStream;
let pc;
let pcId;
let sessionId = null;

async function startExam() {
  // Start session first
  const examId = window.examId || 1; // Replace with actual exam id from context
  const sessionResp = await fetch('/api/proctoring/session/start', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({exam_id: examId})
  });
  const sessionData = await sessionResp.json();
  sessionId = sessionData.session_id;

  localStream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });
  document.getElementById('video-preview').srcObject = localStream;

  pc = new RTCPeerConnection({iceServers:[{urls: window.STUN_URL||'stun:stun.l.google.com:19302'}]});
  localStream.getTracks().forEach(t=>pc.addTrack(t, localStream));
  const offer = await pc.createOffer();
  await pc.setLocalDescription(offer);
  const ans = await fetch('/webrtc/offer',{
    method:'POST',
    headers:{'Content-Type':'application/json'},
    body:JSON.stringify({sdp:offer.sdp,type:offer.type,session_id:sessionId})
  }).then(r=>r.json());
  await pc.setRemoteDescription(ans);
  pcId = ans.pc_id;
  pc.onicecandidate = e=>{
    if(e.candidate){
      fetch('/webrtc/candidate',{
        method:'POST',
        headers:{'Content-Type':'application/json'},
        body:JSON.stringify({pc_id: pcId, candidate: e.candidate})
      });
    }
  };
  // TODO: Wire ontrack to analysis pipeline
}
window.onload = startExam;
