#!/usr/bin/env python3
"""
Test script to verify all dependencies are installed and working
"""

def test_imports():
    """Test all required imports"""
    print("🔍 Testing all dependencies...")
    
    try:
        # Core Flask dependencies
        import flask
        print("✅ Flask:", flask.__version__)
        
        import flask_sqlalchemy
        print("✅ Flask-SQLAlchemy")
        
        import flask_login
        print("✅ Flask-Login")
        
        import flask_socketio
        print("✅ Flask-SocketIO")
        
        import flask_cors
        print("✅ Flask-CORS")
        
        import flask_session
        print("✅ Flask-Session")
        
        import flask_migrate
        print("✅ Flask-Migrate")
        
        # AI/ML dependencies
        import cv2
        print("✅ OpenCV:", cv2.__version__)
        
        import face_recognition
        print("✅ face-recognition")
        
        import numpy as np
        print("✅ NumPy:", np.__version__)
        
        import ultralytics
        print("✅ Ultralytics")
        
        import librosa
        print("✅ Librosa:", librosa.__version__)
        
        import mediapipe as mp
        print("✅ MediaPipe:", mp.__version__)
        
        import pydub
        print("✅ PyDub")
        
        # WebRTC dependencies
        import aiortc
        print("✅ aiortc")
        
        import av
        print("✅ av:", av.__version__)
        
        # Database dependencies
        import sqlalchemy
        print("✅ SQLAlchemy:", sqlalchemy.__version__)
        
        import psycopg2
        print("✅ psycopg2")
        
        import redis
        print("✅ Redis")
        
        import alembic
        print("✅ Alembic")
        
        # Utility dependencies
        import PyPDF2
        print("✅ PyPDF2")
        
        import requests
        print("✅ Requests")
        
        import werkzeug
        print("✅ Werkzeug")
        
        # Testing dependencies
        import pytest
        print("✅ Pytest")
        
        print("\n🎉 All dependencies imported successfully!")
        return True
        
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        return False
    except Exception as e:
        print(f"⚠️ Error importing: {e}")
        return False

def test_app_import():
    """Test importing the main app"""
    print("\n🔍 Testing app import...")
    try:
        import app
        print("✅ App module imported successfully!")
        return True
    except Exception as e:
        print(f"❌ Error importing app: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("🧪 AI EXAMINER DEPENDENCY TEST")
    print("=" * 50)
    
    deps_ok = test_imports()
    app_ok = test_app_import()
    
    print("\n" + "=" * 50)
    if deps_ok and app_ok:
        print("🎉 ALL TESTS PASSED! Ready to run the application.")
        print("\nTo start the app, run:")
        print("python app.py")
    else:
        print("❌ Some tests failed. Please install missing dependencies.")
    print("=" * 50)
