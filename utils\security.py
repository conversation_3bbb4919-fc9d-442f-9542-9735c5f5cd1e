import secrets
from werkzeug.security import generate_password_hash, check_password_hash
from flask import session

def hash_password(password):
    return generate_password_hash(password)

def verify_password(hash, password):
    return check_password_hash(hash, password)

def generate_token():
    return secrets.token_urlsafe(32)

def set_csrf_token():
    token = generate_token()
    session['csrf_token'] = token
    return token

def validate_csrf_token(token):
    return session.get('csrf_token') == token
