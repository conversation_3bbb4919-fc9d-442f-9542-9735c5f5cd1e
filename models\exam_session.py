from sqlalchemy import <PERSON>umn, Integer, Foreign<PERSON>ey, DateTime, Float, String
from sqlalchemy.orm import relationship
from . import db

class ExamSession(db.Model):
    __tablename__ = 'exam_sessions'
    id = Column(Integer, primary_key=True)
    student_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    exam_id = Column(Integer, ForeignKey('exams.id'), nullable=False)
    start_time = Column(DateTime, nullable=False)
    end_time = Column(DateTime, nullable=True)
    trust_score = Column(Float, nullable=True)
    video_archive = Column(String(256), nullable=True)
    audio_archive = Column(String(256), nullable=True)
    screen_archive = Column(String(256), nullable=True)

    # Relationships
    student = relationship("User", foreign_keys=[student_id])
    exam = relationship("Exam", foreign_keys=[exam_id])

    def __init__(self, student_id, exam_id, start_time, end_time=None, trust_score=None, video_archive=None, audio_archive=None, screen_archive=None):
        self.student_id = student_id
        self.exam_id = exam_id
        self.start_time = start_time
        self.end_time = end_time
        self.trust_score = trust_score
        self.video_archive = video_archive
        self.audio_archive = audio_archive
        self.screen_archive = screen_archive
