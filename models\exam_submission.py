from sqlalchemy import Column, Integer, String, ForeignKey, Text, DateTime, JSON
from datetime import datetime, timezone
from . import db

class ExamSubmission(db.Model):
    """
    Stores student exam submissions with their answers
    """
    __tablename__ = 'exam_submissions'
    
    id = Column(Integer, primary_key=True)
    student_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    exam_id = Column(Integer, ForeignKey('exams.id'), nullable=False)
    session_id = Column(Integer, ForeignKey('exam_sessions.id'), nullable=True)
    
    # Store answers as JSON for flexibility
    answers = Column(JSON, nullable=False)  # {"1": "answer1", "2": "answer2", etc.}
    
    # Submission metadata
    submitted_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    time_taken_minutes = Column(Integer, nullable=True)  # How long they took
    score = Column(Integer, nullable=True)  # Score if auto-graded
    max_score = Column(Integer, nullable=True)  # Maximum possible score
    
    # Status tracking
    status = Column(String(20), default='submitted')  # submitted, graded, reviewed
    
    def __init__(self, student_id, exam_id, answers, session_id=None, time_taken_minutes=None):
        self.student_id = student_id
        self.exam_id = exam_id
        self.answers = answers
        self.session_id = session_id
        self.time_taken_minutes = time_taken_minutes

class ExamQuestion(db.Model):
    """
    Stores parsed questions from PDF with their options
    """
    __tablename__ = 'exam_questions'
    
    id = Column(Integer, primary_key=True)
    exam_id = Column(Integer, ForeignKey('exams.id'), nullable=False)
    question_number = Column(Integer, nullable=False)
    question_text = Column(Text, nullable=False)
    question_type = Column(String(20), nullable=False)  # multiple_choice, true_false, essay, short_answer
    
    # For multiple choice questions
    options = Column(JSON, nullable=True)  # {"A": "option1", "B": "option2", etc.}
    correct_answer = Column(String(10), nullable=True)  # "A", "B", "C", "D" for MC questions
    
    # Points for this question
    points = Column(Integer, default=1)
    
    def __init__(self, exam_id, question_number, question_text, question_type, options=None, correct_answer=None, points=1):
        self.exam_id = exam_id
        self.question_number = question_number
        self.question_text = question_text
        self.question_type = question_type
        self.options = options
        self.correct_answer = correct_answer
        self.points = points
