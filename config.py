import os

class Config:
    SECRET_KEY = os.environ.get("SECRET_KEY", "dev-secret-key")
    SQLALCHEMY_DATABASE_URI = os.environ.get("DATABASE_URL", "sqlite:///ai_examiner.db")
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SESSION_TYPE = "filesystem"
    REDIS_URL = os.environ.get("REDIS_URL", "redis://localhost:6379/0")
    AI_MODELS_PATH = os.environ.get("AI_MODELS_PATH", "./models")
    FILE_STORAGE_PATH = os.environ.get("FILE_STORAGE_PATH", "./storage")
    # WebRTC/STUN/TURN config
    WEBRTC_STUN_SERVER = os.environ.get("WEBRTC_STUN_SERVER", "stun:stun.l.google.com:19302")
    WEBRTC_TURN_SERVER = os.environ.get("WEBRTC_TURN_SERVER", "")
    # Add more config as needed
    YOLO_MODEL_PATH = os.environ.get("YOLO_MODEL_PATH", "yolov8n.pt")
    YOLO_DEVICE = os.environ.get("YOLO_DEVICE", "cpu")
