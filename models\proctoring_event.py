from sqlalchemy import <PERSON>umn, Integer, ForeignKey, DateTime, String, Float, JSON
from . import db

class ProctoringEvent(db.Model):
    __tablename__ = 'proctoring_events'
    id = Column(Integer, primary_key=True)
    session_id = Column(Integer, ForeignKey('exam_sessions.id'), nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)
    event_type = Column(String(64), nullable=False)
    severity_score = Column(Float, nullable=True)
    event_metadata = Column(JSON, nullable=True)

    def __init__(self, session_id, timestamp, event_type, severity_score=None, event_metadata=None):
        self.session_id = session_id
        self.timestamp = timestamp
        self.event_type = event_type
        self.severity_score = severity_score
        self.event_metadata = event_metadata
