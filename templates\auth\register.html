{% extends 'base.html' %}
{% block title %}Register | AI Examiner{% endblock %}
{% block content %}
<div style="max-width: 500px; margin: 0 auto;">
  <h2>Create Account</h2>

  {% if error %}
    <div class="alert">{{ error }}</div>
  {% endif %}

  <form id="register-form" method="post" action="/api/auth/register">
    <div style="margin-bottom: 1rem;">
      <label for="username">Username:</label><br>
      <input type="text" id="username" name="username" required style="width: 100%; box-sizing: border-box;">
    </div>

    <div style="margin-bottom: 1rem;">
      <label for="password">Password:</label><br>
      <input type="password" id="password" name="password" required style="width: 100%; box-sizing: border-box;">
    </div>

    <div style="margin-bottom: 1rem;">
      <label for="role">Role:</label><br>
      <select id="role" name="role" required style="width: 100%; box-sizing: border-box;">
        <option value="">Select your role</option>
        <option value="student">Student</option>
        <option value="proctor">Proctor</option>
      </select>
    </div>

    <div style="margin-bottom: 1rem;">
      <label>Face Recognition (Optional):</label><br>
      <div style="text-align: center; margin: 1rem 0;">
        <video id="face-cam" class="video-preview" style="display: none;" autoplay></video>
        <canvas id="face-canvas" class="video-preview" style="display: none;"></canvas>
        <div id="camera-placeholder" style="width: 320px; height: 240px; background: #f0f0f0; border: 2px dashed #ccc; display: flex; align-items: center; justify-content: center; margin: 0 auto; border-radius: 8px;">
          <span style="color: #666;">Camera not started</span>
        </div>
      </div>
      <div style="text-align: center;">
        <button type="button" id="start-camera" onclick="startCamera()">Start Camera</button>
        <button type="button" id="capture-face" onclick="captureFace()" style="display: none;">Capture Face</button>
        <button type="button" id="retake-face" onclick="retakeFace()" style="display: none;">Retake</button>
      </div>
      <input type="hidden" name="face_encoding" id="face_encoding">
      <div id="capture-status" style="margin-top: 0.5rem; text-align: center; color: #666;"></div>
    </div>

    <button type="submit" style="width: 100%; padding: 0.75rem; font-size: 1.1rem;">Create Account</button>
  </form>

  <p style="text-align: center; margin-top: 1.5rem;">
    Already have an account? <a href="/api/auth/login">Login here</a>
  </p>
</div>

<script>
let stream = null;
let faceCaptured = false;

async function startCamera() {
  try {
    stream = await navigator.mediaDevices.getUserMedia({ video: true });
    const video = document.getElementById('face-cam');
    const placeholder = document.getElementById('camera-placeholder');

    video.srcObject = stream;
    video.style.display = 'block';
    placeholder.style.display = 'none';

    document.getElementById('start-camera').style.display = 'none';
    document.getElementById('capture-face').style.display = 'inline-block';
    document.getElementById('capture-status').textContent = 'Camera ready - position your face in the frame';
  } catch (err) {
    document.getElementById('capture-status').textContent = 'Camera access denied or not available';
    console.error('Error accessing camera:', err);
  }
}

function captureFace() {
  const video = document.getElementById('face-cam');
  const canvas = document.getElementById('face-canvas');
  const ctx = canvas.getContext('2d');

  canvas.width = video.videoWidth;
  canvas.height = video.videoHeight;
  ctx.drawImage(video, 0, 0);

  // Convert to base64 for simple storage (in real implementation, this would be processed for face encoding)
  const imageData = canvas.toDataURL('image/jpeg', 0.8);
  document.getElementById('face_encoding').value = imageData;

  // Show captured image
  canvas.style.display = 'block';
  video.style.display = 'none';

  document.getElementById('capture-face').style.display = 'none';
  document.getElementById('retake-face').style.display = 'inline-block';
  document.getElementById('capture-status').textContent = 'Face captured successfully!';

  faceCaptured = true;
}

function retakeFace() {
  const video = document.getElementById('face-cam');
  const canvas = document.getElementById('face-canvas');

  canvas.style.display = 'none';
  video.style.display = 'block';

  document.getElementById('capture-face').style.display = 'inline-block';
  document.getElementById('retake-face').style.display = 'none';
  document.getElementById('capture-status').textContent = 'Camera ready - position your face in the frame';
  document.getElementById('face_encoding').value = '';

  faceCaptured = false;
}

// Clean up camera when page unloads
window.addEventListener('beforeunload', function() {
  if (stream) {
    stream.getTracks().forEach(track => track.stop());
  }
});
</script>
{% endblock %}
