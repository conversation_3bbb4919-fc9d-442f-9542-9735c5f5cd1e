{% extends 'base.html' %}
{% block title %}Create Exam | AI Examiner{% endblock %}
{% block content %}
<div style="max-width: 800px; margin: 0 auto;">
  <h2>Create New Exam</h2>
  
  {% if error %}
    <div class="alert">{{ error }}</div>
  {% endif %}
  
  <form id="create-exam-form" method="post" action="/api/exam/create" enctype="multipart/form-data">
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
      <!-- Left Column -->
      <div>
        <div style="margin-bottom: 1rem;">
          <label for="title">Exam Title:</label><br>
          <input type="text" id="title" name="title" required style="width: 100%; box-sizing: border-box;" placeholder="Enter exam title">
        </div>
        
        <div style="margin-bottom: 1rem;">
          <label for="duration_minutes">Duration (minutes):</label><br>
          <input type="number" id="duration_minutes" name="duration_minutes" required min="1" max="480" style="width: 100%; box-sizing: border-box;" placeholder="e.g., 60">
        </div>
        
        <div style="margin-bottom: 1rem;">
          <label for="question_count">Number of Questions:</label><br>
          <input type="number" id="question_count" name="question_count" min="1" max="200" style="width: 100%; box-sizing: border-box;" placeholder="e.g., 25">
        </div>
        
        <div style="margin-bottom: 1rem;">
          <label for="question_types">Question Types:</label><br>
          <div style="margin-top: 0.5rem;">
            <label style="display: block; margin-bottom: 0.3rem;">
              <input type="checkbox" name="question_types" value="multiple_choice"> Multiple Choice
            </label>
            <label style="display: block; margin-bottom: 0.3rem;">
              <input type="checkbox" name="question_types" value="true_false"> True/False
            </label>
            <label style="display: block; margin-bottom: 0.3rem;">
              <input type="checkbox" name="question_types" value="short_answer"> Short Answer
            </label>
            <label style="display: block; margin-bottom: 0.3rem;">
              <input type="checkbox" name="question_types" value="essay"> Essay
            </label>
            <label style="display: block; margin-bottom: 0.3rem;">
              <input type="checkbox" name="question_types" value="fill_blank"> Fill in the Blank
            </label>
          </div>
        </div>
      </div>
      
      <!-- Right Column -->
      <div>
        <div style="margin-bottom: 1rem;">
          <label for="pdf_file">Upload Exam PDF:</label><br>
          <input type="file" id="pdf_file" name="pdf_file" accept=".pdf" style="width: 100%; box-sizing: border-box; padding: 0.5rem; border: 2px dashed #ccc; border-radius: 4px;">
          <small style="color: #666; display: block; margin-top: 0.5rem;">
            Upload a PDF file containing the exam questions. Maximum file size: 10MB
          </small>
        </div>
        
        <div style="margin-bottom: 1rem;">
          <label for="instructions">Exam Instructions:</label><br>
          <textarea id="instructions" name="instructions" rows="8" style="width: 100%; box-sizing: border-box; resize: vertical;" placeholder="Enter instructions for students taking this exam..."></textarea>
        </div>
      </div>
    </div>
    
    <!-- PDF Preview -->
    <div id="pdf-preview" style="display: none; margin-bottom: 2rem; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
      <h4>PDF Preview:</h4>
      <div id="pdf-info" style="margin-bottom: 1rem; color: #666;"></div>
      <canvas id="pdf-canvas" style="max-width: 100%; border: 1px solid #ddd;"></canvas>
    </div>
    
    <!-- Action Buttons -->
    <div style="display: flex; gap: 1rem; justify-content: flex-end;">
      <button type="button" onclick="window.location.href='/api/exam/dashboard'" style="background: #6c757d; border: none; color: white; padding: 0.75rem 1.5rem; border-radius: 4px; cursor: pointer;">
        Cancel
      </button>
      <button type="submit" style="background: #28a745; border: none; color: white; padding: 0.75rem 1.5rem; border-radius: 4px; cursor: pointer; font-size: 1rem;">
        Create Exam
      </button>
    </div>
  </form>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
<script>
// PDF.js setup
pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';

document.getElementById('pdf_file').addEventListener('change', function(e) {
  const file = e.target.files[0];
  if (file && file.type === 'application/pdf') {
    const fileReader = new FileReader();
    fileReader.onload = function() {
      const typedarray = new Uint8Array(this.result);
      
      pdfjsLib.getDocument(typedarray).promise.then(function(pdf) {
        document.getElementById('pdf-preview').style.display = 'block';
        document.getElementById('pdf-info').innerHTML = `
          <strong>File:</strong> ${file.name}<br>
          <strong>Size:</strong> ${(file.size / 1024 / 1024).toFixed(2)} MB<br>
          <strong>Pages:</strong> ${pdf.numPages}
        `;
        
        // Render first page
        pdf.getPage(1).then(function(page) {
          const scale = 1.5;
          const viewport = page.getViewport({ scale: scale });
          
          const canvas = document.getElementById('pdf-canvas');
          const context = canvas.getContext('2d');
          canvas.height = viewport.height;
          canvas.width = viewport.width;
          
          const renderContext = {
            canvasContext: context,
            viewport: viewport
          };
          page.render(renderContext);
        });
      }).catch(function(error) {
        alert('Error loading PDF: ' + error.message);
      });
    };
    fileReader.readAsArrayBuffer(file);
  } else if (file) {
    alert('Please select a valid PDF file.');
    e.target.value = '';
  }
});

// Form validation
document.getElementById('create-exam-form').addEventListener('submit', function(e) {
  const questionTypes = document.querySelectorAll('input[name="question_types"]:checked');
  if (questionTypes.length === 0) {
    alert('Please select at least one question type.');
    e.preventDefault();
    return false;
  }
  
  const pdfFile = document.getElementById('pdf_file').files[0];
  if (pdfFile && pdfFile.size > 10 * 1024 * 1024) { // 10MB limit
    alert('PDF file size must be less than 10MB.');
    e.preventDefault();
    return false;
  }
  
  return true;
});
</script>
{% endblock %}
