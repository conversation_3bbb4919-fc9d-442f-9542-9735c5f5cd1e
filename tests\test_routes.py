import pytest
from app import app
from models import db, User, Exam, ExamSession, ProctoringEvent
from flask import g
from datetime import datetime, timezone

@pytest.fixture
def test_app():
    app.config['TESTING'] = True
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
    with app.app_context():
        db.create_all()
        yield app.test_client()
        db.session.remove()
        db.drop_all()

def register(client, username, password, role):
    return client.post('/api/auth/register', data={'username': username, 'password': password, 'role': role})

def login(client, username, password):
    return client.post('/api/auth/login', data={'username': username, 'password': password})

def logout(client):
    return client.get('/api/auth/logout')

def test_auth_flow(test_app):
    client = test_app
    r = register(client, 'alice', 'pw', 'student')
    assert r.status_code in (200, 302)
    r = login(client, 'alice', 'pw')
    assert r.status_code in (200, 302)
    r = client.get('/api/proctoring/active_sessions')
    assert r.status_code in (200, 302, 401, 403)
    logout(client)
    r = client.get('/api/proctoring/active_sessions')
    assert r.status_code in (401, 302, 403)

def test_exam_crud(test_app):
    client = test_app
    register(client, 'proctor', 'pw', 'proctor')
    login(client, 'proctor', 'pw')
    r = client.post('/api/exam/create', json={'title': 'Exam1', 'duration_minutes': 60})
    assert r.status_code == 200
    r = client.get('/api/exam/list')
    assert r.status_code == 200
    logout(client)
    register(client, 'student', 'pw', 'student')
    login(client, 'student', 'pw')
    r = client.post('/api/exam/create', json={'title': 'Exam2', 'duration_minutes': 60})
    assert r.status_code == 403

def test_proctoring_events(test_app):
    client = test_app
    register(client, 'proctor', 'pw', 'proctor')
    login(client, 'proctor', 'pw')
    # Create exam and session
    r = client.post('/api/exam/create', json={'title': 'Exam1', 'duration_minutes': 60})
    exam_id = r.get_json()['exam_id']
    session = ExamSession(student_id=1, exam_id=exam_id, start_time=datetime.now(timezone.utc))
    db.session.add(session)
    db.session.commit()
    event = ProctoringEvent(session_id=session.id, timestamp=datetime.now(timezone.utc), event_type='test', severity_score=0.5, event_metadata={'foo':'bar'})
    db.session.add(event)
    db.session.commit()
    r = client.get(f'/api/proctoring/events/{session.id}')
    assert r.status_code == 200
    data = r.get_json()
    assert isinstance(data, list)
    assert 'timestamp' in data[0] and 'T' in data[0]['timestamp']
    assert 'metadata' in data[0]
