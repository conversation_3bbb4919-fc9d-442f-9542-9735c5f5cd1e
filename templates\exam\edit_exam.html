{% extends 'base.html' %}
{% block title %}Edit Exam: {{ exam.title }} | AI Examiner{% endblock %}
{% block content %}
<div style="max-width: 800px; margin: 0 auto;">
  <div style="background: white; border: 1px solid #dee2e6; border-radius: 8px; padding: 2rem;">
    <h2 style="margin-top: 0; color: #1a2233;">✏️ Edit Exam</h2>
    
    <form method="POST" style="margin-top: 2rem;">
      <div style="margin-bottom: 1.5rem;">
        <label for="title" style="display: block; margin-bottom: 0.5rem; font-weight: bold;">Exam Title:</label>
        <input type="text" id="title" name="title" value="{{ exam.title }}" required 
               style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;">
      </div>

      <div style="margin-bottom: 1.5rem;">
        <label for="duration" style="display: block; margin-bottom: 0.5rem; font-weight: bold;">Duration (minutes):</label>
        <input type="number" id="duration" name="duration" value="{{ exam.duration_minutes }}" min="1" max="480" required 
               style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;">
      </div>

      <div style="margin-bottom: 1.5rem;">
        <label for="question_count" style="display: block; margin-bottom: 0.5rem; font-weight: bold;">Number of Questions:</label>
        <input type="number" id="question_count" name="question_count" value="{{ exam.question_count or '' }}" min="1" max="200" 
               style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;">
        <small style="color: #6c757d;">Leave empty if not specified</small>
      </div>

      <div style="margin-bottom: 1.5rem;">
        <label style="display: block; margin-bottom: 0.5rem; font-weight: bold;">Question Types:</label>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 0.5rem;">
          {% set current_types = exam.question_types.split(',') if exam.question_types else [] %}
          <label style="display: flex; align-items: center; cursor: pointer;">
            <input type="checkbox" name="question_types" value="multiple_choice" 
                   {% if 'multiple_choice' in current_types %}checked{% endif %}
                   style="margin-right: 0.5rem;">
            Multiple Choice
          </label>
          <label style="display: flex; align-items: center; cursor: pointer;">
            <input type="checkbox" name="question_types" value="true_false" 
                   {% if 'true_false' in current_types %}checked{% endif %}
                   style="margin-right: 0.5rem;">
            True/False
          </label>
          <label style="display: flex; align-items: center; cursor: pointer;">
            <input type="checkbox" name="question_types" value="short_answer" 
                   {% if 'short_answer' in current_types %}checked{% endif %}
                   style="margin-right: 0.5rem;">
            Short Answer
          </label>
          <label style="display: flex; align-items: center; cursor: pointer;">
            <input type="checkbox" name="question_types" value="essay" 
                   {% if 'essay' in current_types %}checked{% endif %}
                   style="margin-right: 0.5rem;">
            Essay
          </label>
        </div>
      </div>

      <div style="margin-bottom: 1.5rem;">
        <label for="instructions" style="display: block; margin-bottom: 0.5rem; font-weight: bold;">Instructions for Students:</label>
        <textarea id="instructions" name="instructions" rows="4" 
                  style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; resize: vertical;"
                  placeholder="Enter any special instructions for students taking this exam...">{{ exam.instructions or '' }}</textarea>
      </div>

      <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; padding: 1rem; margin-bottom: 1.5rem;">
        <h4 style="margin-top: 0; color: #495057;">📄 Current PDF</h4>
        {% if exam.pdf_file_path %}
          <p style="margin-bottom: 0.5rem;">
            <strong>File:</strong> {{ exam.pdf_file_path }}
          </p>
          <p style="margin-bottom: 0; color: #6c757d;">
            <small>To change the PDF, you'll need to create a new exam. PDF editing is not supported yet.</small>
          </p>
        {% else %}
          <p style="margin-bottom: 0; color: #6c757d;">No PDF file uploaded for this exam.</p>
        {% endif %}
      </div>

      <div style="background: #e7f3ff; border: 1px solid #b3d9ff; border-radius: 4px; padding: 1rem; margin-bottom: 1.5rem;">
        <h4 style="margin-top: 0; color: #0056b3;">📊 Current Questions</h4>
        {% set question_count_actual = exam.questions|length if exam.questions else 0 %}
        <p style="margin-bottom: 0.5rem;">
          <strong>Parsed Questions:</strong> {{ question_count_actual }}
        </p>
        {% if question_count_actual > 0 %}
          <p style="margin-bottom: 0; color: #0056b3;">
            <small>Questions have been parsed from the PDF and are ready for students.</small>
          </p>
        {% else %}
          <p style="margin-bottom: 0; color: #856404;">
            <small>No questions have been parsed yet. Use "Parse Questions from PDF" in the exam details.</small>
          </p>
        {% endif %}
      </div>

      <div style="display: flex; gap: 1rem; justify-content: flex-end;">
        <a href="{{ url_for('exam.exam_details', exam_id=exam.id) }}" 
           style="background: #6c757d; border: none; color: white; padding: 0.75rem 1.5rem; border-radius: 4px; text-decoration: none; display: inline-block;">
          Cancel
        </a>
        <button type="submit" 
                style="background: #28a745; border: none; color: white; padding: 0.75rem 1.5rem; border-radius: 4px; cursor: pointer; font-size: 1rem;">
          💾 Save Changes
        </button>
      </div>
    </form>
  </div>

  <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 1.5rem; margin-top: 2rem;">
    <h4 style="margin-top: 0; color: #856404;">⚠️ Important Notes</h4>
    <ul style="margin-bottom: 0; color: #856404;">
      <li>Changing exam details will not affect students who have already submitted their answers.</li>
      <li>If you change the duration, it will only apply to new exam sessions.</li>
      <li>To change the PDF file, you'll need to create a new exam.</li>
      <li>Parsed questions are not affected by changes to question count or types - they reflect the actual PDF content.</li>
    </ul>
  </div>
</div>

<script>
// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
  const title = document.getElementById('title').value.trim();
  const duration = parseInt(document.getElementById('duration').value);
  
  if (!title) {
    alert('Please enter an exam title.');
    e.preventDefault();
    return;
  }
  
  if (duration < 1 || duration > 480) {
    alert('Duration must be between 1 and 480 minutes.');
    e.preventDefault();
    return;
  }
  
  // Check if at least one question type is selected
  const questionTypes = document.querySelectorAll('input[name="question_types"]:checked');
  if (questionTypes.length === 0) {
    if (!confirm('No question types selected. Continue anyway?')) {
      e.preventDefault();
      return;
    }
  }
});

// Auto-save draft functionality
let saveTimeout;
function autoSave() {
  clearTimeout(saveTimeout);
  saveTimeout = setTimeout(() => {
    const formData = {
      title: document.getElementById('title').value,
      duration: document.getElementById('duration').value,
      question_count: document.getElementById('question_count').value,
      instructions: document.getElementById('instructions').value
    };
    localStorage.setItem('exam_edit_draft_{{ exam.id }}', JSON.stringify(formData));
  }, 1000);
}

// Add auto-save listeners
document.getElementById('title').addEventListener('input', autoSave);
document.getElementById('duration').addEventListener('input', autoSave);
document.getElementById('question_count').addEventListener('input', autoSave);
document.getElementById('instructions').addEventListener('input', autoSave);

// Load draft on page load
window.addEventListener('load', function() {
  const draft = localStorage.getItem('exam_edit_draft_{{ exam.id }}');
  if (draft) {
    try {
      const data = JSON.parse(draft);
      if (confirm('A draft of your changes was found. Would you like to restore it?')) {
        document.getElementById('title').value = data.title || '';
        document.getElementById('duration').value = data.duration || '';
        document.getElementById('question_count').value = data.question_count || '';
        document.getElementById('instructions').value = data.instructions || '';
      }
    } catch (e) {
      console.error('Error loading draft:', e);
    }
  }
});

// Clear draft on successful submit
document.querySelector('form').addEventListener('submit', function() {
  localStorage.removeItem('exam_edit_draft_{{ exam.id }}');
});
</script>
{% endblock %}
