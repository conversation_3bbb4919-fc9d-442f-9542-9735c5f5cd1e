from sqlalchemy import <PERSON>umn, Inte<PERSON>, <PERSON>, Foreign<PERSON>ey, CheckConstraint, Text, DateTime
from datetime import datetime, timezone
from . import db

# Maximum allowed length for exam titles
MAX_TITLE_LEN = 128

class Exam(db.Model):
    """
    Represents an exam configuration and metadata.
    - title: up to MAX_TITLE_LEN characters, required
    - duration_minutes: must be > 0 (enforced by DB constraint)
    - created_by_id: foreign key to Use<PERSON>, restricts deletion of referenced user
    - pdf_file_path: path to uploaded PDF file
    - question_count: number of questions in the exam
    - question_types: types of questions (multiple choice, essay, etc.)
    - instructions: exam instructions for students
    """
    __tablename__ = 'exams'
    id = Column(Integer, primary_key=True)
    title = Column(String(MAX_TITLE_LEN), nullable=False)
    duration_minutes = Column(Integer, CheckConstraint('duration_minutes > 0', name='ck_exam_duration_positive'), nullable=False)
    created_by_id = Column(Integer, ForeignKey('users.id'), nullable=False)

    # New fields for enhanced exam functionality
    pdf_file_path = Column(String(255), nullable=True)  # Path to uploaded PDF
    question_count = Column(Integer, nullable=True)
    question_types = Column(String(255), nullable=True)  # JSON string or comma-separated
    instructions = Column(Text, nullable=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    def __init__(self, title, duration_minutes, created_by_id, pdf_file_path=None,
                 question_count=None, question_types=None, instructions=None):
        self.title = title
        self.duration_minutes = duration_minutes
        self.created_by_id = created_by_id
        self.pdf_file_path = pdf_file_path
        self.question_count = question_count
        self.question_types = question_types
        self.instructions = instructions
