
from ultralytics import YOLO
from flask import current_app
from threading import Lock

_model = None
_lock = Lock()

def get_model():
    global _model
    if _model is None:
        with _lock:
            if _model is None:
                path = current_app.config.get('YOLO_MODEL_PATH','yolov8n.pt')
                _model = YOLO(path)
    return _model

def detect_objects(frame):
    model = get_model()
    results = model(frame)
    # TODO: Parse results for prohibited objects/persons
    return []
