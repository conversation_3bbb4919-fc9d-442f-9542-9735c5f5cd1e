import os
from werkzeug.utils import secure_filename
from flask import current_app

def save_file(file, subdir):
    storage_path = os.path.join(current_app.config['FILE_STORAGE_PATH'], subdir)
    os.makedirs(storage_path, exist_ok=True)
    filename = secure_filename(file.filename)
    file_path = os.path.join(storage_path, filename)
    file.save(file_path)
    return file_path

def cleanup_file(file_path):
    if os.path.exists(file_path):
        os.remove(file_path)
