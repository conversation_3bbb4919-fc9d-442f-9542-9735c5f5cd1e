/* main.css - Responsive, modern UI for AI Examiner */
body {
  font-family: 'Segoe UI', Arial, sans-serif;
  background: #f7f9fa;
  margin: 0;
  color: #222;
}
nav {
  background: #1a2233;
  color: #fff;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.container {
  max-width: 900px;
  margin: 2rem auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.07);
  padding: 2rem;
}
input, button, select {
  font-size: 1rem;
  padding: 0.5rem;
  margin: 0.5rem 0;
  border-radius: 4px;
  border: 1px solid #ccc;
}
button {
  background: #1a2233;
  color: #fff;
  border: none;
  cursor: pointer;
  transition: background 0.2s;
}
button:hover {
  background: #2d3a5a;
}
.video-preview {
  width: 320px;
  height: 240px;
  background: #222;
  border-radius: 8px;
  margin: 1rem 0;
  object-fit: cover;
}
.grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}
.alert {
  padding: 0.75rem 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  background: #ffe6e6;
  color: #a33;
  border: 1px solid #fbb;
}
