# AI Examiner

AI Examiner is a real-time AI proctoring system for online exams, featuring facial recognition, gaze tracking, object detection, audio analysis, and a modern dashboard for human review.

## Features
- Real-time video/audio analysis (face, gaze, objects, speech)
- WebRTC-based secure browser client
- Flask backend with AI modules (JSON API only)
- PostgreSQL + Redis for storage
- React dashboard for proctors (SPA)
- Dockerized deployment

## Setup
1. Clone repo and copy `.env.example` to `.env`
2. `docker-compose up --build`
3. Access backend API at http://localhost:5000/api, frontend at http://localhost:3000

## Database Migrations

This project uses Flask-Migrate (Alembic) for schema management:

```
export FLASK_APP=app.py
flask db init
flask db migrate -m "init"
flask db upgrade
```

## Configuration
- See `.env.example` for environment variables

## Contributing
PRs welcome! See `tests/` for test coverage.
