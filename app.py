# AI Examiner - Flask Backend Entry Point
from flask import Flask
from flask_login import Login<PERSON>anager
from flask_socketio import So<PERSON><PERSON>
from flask_cors import CORS
from flask_session import Session
import redis
from config import Config
from models import db
from models.user import User
from routes import register_blueprints
from flask_migrate import Migrate
from utils.database import init_db

app = Flask(__name__)
app.config.from_object(Config)
CORS(app)
db.init_app(app)
if app.config['SESSION_TYPE'] == 'redis':
    try:
        app.config['SESSION_REDIS'] = redis.from_url(app.config['REDIS_URL'])
    except:
        app.config['SESSION_TYPE'] = 'filesystem'
Session(app)
login_manager = LoginManager(app)
login_manager.login_view = 'auth.login'  # type: ignore

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

migrate = Migrate(app, db)

with app.app_context():
    init_db(app)

socketio = SocketIO(app, cors_allowed_origins="*")
register_blueprints(app)

# Add root route
@app.route('/')
def index():
    from flask import redirect, url_for
    return redirect(url_for('auth.login_page'))

if __name__ == "__main__":
    socketio.run(app, host="0.0.0.0", port=5000, debug=False)
