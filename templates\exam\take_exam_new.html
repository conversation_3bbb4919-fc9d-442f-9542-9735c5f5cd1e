{% extends 'base.html' %}
{% block title %}Take Exam: {{ exam.title }} | AI Examiner{% endblock %}
{% block content %}
<div style="max-width: 1200px; margin: 0 auto;">
  <!-- Exam Header -->
  <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 2rem; margin-bottom: 2rem;">
    <h2 style="margin-top: 0; color: #1a2233;">{{ exam.title }}</h2>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1rem;">
      <div>
        <strong>Duration:</strong> {{ exam.duration_minutes }} minutes
      </div>
      <div>
        <strong>Questions:</strong> {{ questions|length }}
      </div>
      <div>
        <strong>Time Remaining:</strong> <span id="timer" style="color: #dc3545; font-weight: bold;">{{ exam.duration_minutes }}:00</span>
      </div>
    </div>
    {% if exam.instructions %}
    <div style="background: #e9ecef; padding: 1rem; border-radius: 4px; margin-top: 1rem;">
      <strong>Instructions:</strong> {{ exam.instructions }}
    </div>
    {% endif %}
  </div>

  <div style="display: grid; grid-template-columns: 1fr 350px; gap: 2rem;">
    <!-- Questions Area -->
    <div>
      <div style="background: white; border: 1px solid #dee2e6; border-radius: 8px; padding: 1.5rem;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
          <h3 style="margin: 0;">Exam Questions</h3>
          <button id="start-exam" onclick="startExam()" style="background: #28a745; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 4px; cursor: pointer; font-weight: bold;">
            🚀 Start Exam
          </button>
        </div>

        <div id="exam-content" style="display: none;">
          {% if questions %}
            {% for question in questions %}
            <div class="question-container" style="margin-bottom: 2rem; padding: 1.5rem; border: 1px solid #e9ecef; border-radius: 8px;">
              <div style="margin-bottom: 1rem;">
                <strong>Question {{ question.question_number }}:</strong> ({{ question.points }} point{{ 's' if question.points != 1 else '' }})
              </div>
              <div style="margin-bottom: 1.5rem; line-height: 1.6;">
                {{ question.question_text }}
              </div>

              {% if question.question_type == 'multiple_choice' %}
                <div class="options">
                  {% for key, value in question.options.items() %}
                  <label style="display: block; margin-bottom: 0.75rem; cursor: pointer; padding: 0.5rem; border-radius: 4px; transition: background-color 0.2s;" 
                         onmouseover="this.style.backgroundColor='#f8f9fa'" 
                         onmouseout="this.style.backgroundColor='transparent'">
                    <input type="radio" name="question_{{ question.question_number }}" value="{{ key }}" style="margin-right: 0.5rem;">
                    <strong>{{ key }})</strong> {{ value }}
                  </label>
                  {% endfor %}
                </div>
              {% elif question.question_type == 'true_false' %}
                <div class="options">
                  <label style="display: block; margin-bottom: 0.75rem; cursor: pointer; padding: 0.5rem; border-radius: 4px;" 
                         onmouseover="this.style.backgroundColor='#f8f9fa'" 
                         onmouseout="this.style.backgroundColor='transparent'">
                    <input type="radio" name="question_{{ question.question_number }}" value="True" style="margin-right: 0.5rem;">
                    <strong>True</strong>
                  </label>
                  <label style="display: block; margin-bottom: 0.75rem; cursor: pointer; padding: 0.5rem; border-radius: 4px;" 
                         onmouseover="this.style.backgroundColor='#f8f9fa'" 
                         onmouseout="this.style.backgroundColor='transparent'">
                    <input type="radio" name="question_{{ question.question_number }}" value="False" style="margin-right: 0.5rem;">
                    <strong>False</strong>
                  </label>
                </div>
              {% elif question.question_type == 'short_answer' %}
                <textarea name="question_{{ question.question_number }}" 
                         placeholder="Enter your answer here..."
                         style="width: 100%; height: 100px; border: 1px solid #ddd; border-radius: 4px; padding: 0.75rem; font-family: inherit; resize: vertical;"></textarea>
              {% endif %}
            </div>
            {% endfor %}
          {% else %}
            <div style="text-align: center; padding: 2rem; color: #6c757d;">
              <p>No questions have been parsed for this exam yet.</p>
              <p>Please contact your instructor.</p>
            </div>
          {% endif %}

          <div style="text-align: center; margin-top: 2rem;">
            <button id="submit-exam" onclick="submitExam()" style="background: #dc3545; color: white; border: none; padding: 1rem 2rem; border-radius: 4px; cursor: pointer; font-weight: bold; font-size: 1.1rem; display: none;">
              📤 Submit Exam
            </button>
          </div>
        </div>

        <div id="exam-not-started" style="text-align: center; padding: 3rem; color: #6c757d;">
          <h4>Ready to begin your exam?</h4>
          <p>Click "Start Exam" when you're ready. The timer will begin immediately.</p>
          <p><strong>Important:</strong> Make sure you have a stable internet connection and won't be interrupted.</p>
        </div>
      </div>
    </div>

    <!-- Sidebar -->
    <div>
      <!-- Proctoring Status -->
      <div style="background: white; border: 1px solid #dee2e6; border-radius: 8px; padding: 1.5rem; margin-bottom: 1.5rem;">
        <h4 style="margin-top: 0; color: #6c757d;">🔒 Proctoring Status</h4>
        <div id="proctoring-status">
          <div style="color: #ffc107; margin-bottom: 0.5rem;">⚠️ Camera: Not started</div>
          <div style="color: #ffc107; margin-bottom: 0.5rem;">⚠️ Microphone: Not started</div>
          <div style="color: #ffc107;">⚠️ Screen monitoring: Not started</div>
        </div>
        <button onclick="enableProctoring()" style="width: 100%; background: #17a2b8; color: white; border: none; padding: 0.75rem; border-radius: 4px; cursor: pointer; margin-top: 1rem;">
          🎥 Enable Proctoring
        </button>
      </div>

      <!-- Progress Tracker -->
      <div style="background: white; border: 1px solid #dee2e6; border-radius: 8px; padding: 1.5rem; margin-bottom: 1.5rem;">
        <h4 style="margin-top: 0; color: #6c757d;">📊 Progress</h4>
        <div id="progress-info">
          <div>Questions Answered: <span id="answered-count">0</span> / {{ questions|length }}</div>
          <div style="margin-top: 0.5rem;">
            <div style="background: #e9ecef; height: 8px; border-radius: 4px; overflow: hidden;">
              <div id="progress-bar" style="background: #28a745; height: 100%; width: 0%; transition: width 0.3s;"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div style="background: white; border: 1px solid #dee2e6; border-radius: 8px; padding: 1.5rem;">
        <h4 style="margin-top: 0; color: #6c757d;">⚡ Quick Actions</h4>
        <button onclick="saveProgress()" style="width: 100%; background: #6c757d; color: white; border: none; padding: 0.75rem; border-radius: 4px; cursor: pointer; margin-bottom: 0.5rem;">
          💾 Save Progress
        </button>
        <button onclick="reviewAnswers()" style="width: 100%; background: #6f42c1; color: white; border: none; padding: 0.75rem; border-radius: 4px; cursor: pointer;">
          👁️ Review Answers
        </button>
      </div>
    </div>
  </div>
</div>

<script>
let examStarted = false;
let timeRemaining = {{ exam.duration_minutes }} * 60; // Convert to seconds
let timerInterval;
let startTime;
let proctoringEnabled = false;

function startExam() {
  if (examStarted) return;

  examStarted = true;
  startTime = new Date();
  
  // Show exam content
  document.getElementById('exam-not-started').style.display = 'none';
  document.getElementById('exam-content').style.display = 'block';
  document.getElementById('start-exam').style.display = 'none';
  document.getElementById('submit-exam').style.display = 'inline-block';

  // Start the countdown timer
  timerInterval = setInterval(updateTimer, 1000);
  updateTimer(); // Update immediately

  // Auto-submit when time runs out
  setTimeout(() => {
    if (examStarted) {
      alert('Time is up! Your exam will be submitted automatically.');
      submitExam();
    }
  }, timeRemaining * 1000);

  // Update progress tracking
  updateProgress();
  
  // Add event listeners for answer tracking
  document.querySelectorAll('input[type="radio"], textarea').forEach(input => {
    input.addEventListener('change', updateProgress);
  });
}

function updateTimer() {
  const minutes = Math.floor(timeRemaining / 60);
  const seconds = timeRemaining % 60;
  document.getElementById('timer').textContent = 
    `${minutes}:${seconds.toString().padStart(2, '0')}`;
  
  // Change color when time is running low
  if (timeRemaining <= 300) { // 5 minutes
    document.getElementById('timer').style.color = '#dc3545';
  } else if (timeRemaining <= 600) { // 10 minutes
    document.getElementById('timer').style.color = '#ffc107';
  }
  
  timeRemaining--;
  
  if (timeRemaining < 0) {
    clearInterval(timerInterval);
    submitExam();
  }
}

function updateProgress() {
  const totalQuestions = {{ questions|length }};
  let answeredCount = 0;
  
  // Count answered questions
  for (let i = 1; i <= totalQuestions; i++) {
    const questionInputs = document.querySelectorAll(`input[name="question_${i}"], textarea[name="question_${i}"]`);
    let answered = false;
    
    questionInputs.forEach(input => {
      if (input.type === 'radio' && input.checked) {
        answered = true;
      } else if (input.type !== 'radio' && input.value.trim()) {
        answered = true;
      }
    });
    
    if (answered) answeredCount++;
  }
  
  document.getElementById('answered-count').textContent = answeredCount;
  const percentage = (answeredCount / totalQuestions) * 100;
  document.getElementById('progress-bar').style.width = percentage + '%';
}

function collectAnswers() {
  const answers = {};
  const totalQuestions = {{ questions|length }};
  
  for (let i = 1; i <= totalQuestions; i++) {
    const radioInputs = document.querySelectorAll(`input[name="question_${i}"]:checked`);
    const textInputs = document.querySelectorAll(`textarea[name="question_${i}"]`);
    
    if (radioInputs.length > 0) {
      answers[i] = radioInputs[0].value;
    } else if (textInputs.length > 0 && textInputs[0].value.trim()) {
      answers[i] = textInputs[0].value.trim();
    }
  }
  
  return answers;
}

function submitExam() {
  if (!examStarted) {
    alert('Please start the exam first.');
    return;
  }

  const answers = collectAnswers();
  const timeTaken = Math.round((new Date() - startTime) / 60000); // minutes
  
  if (Object.keys(answers).length === 0) {
    if (!confirm('You haven\'t answered any questions. Are you sure you want to submit?')) {
      return;
    }
  }

  // Clear the timer
  clearInterval(timerInterval);
  examStarted = false;

  // Submit to server
  fetch(`/api/exam/submit/{{ exam.id }}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      answers: answers,
      time_taken_minutes: timeTaken
    })
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      alert(`Exam submitted successfully!\nScore: ${data.score}/${data.max_score} (${data.percentage}%)`);
      window.location.href = '/api/exam/dashboard';
    } else {
      alert('Error submitting exam: ' + data.error);
    }
  })
  .catch(error => {
    console.error('Error:', error);
    alert('Error submitting exam. Please try again.');
  });
}

function enableProctoring() {
  if (proctoringEnabled) {
    alert('Proctoring is already enabled!');
    return;
  }

  // Request camera and microphone permissions
  navigator.mediaDevices.getUserMedia({ video: true, audio: true })
    .then(stream => {
      proctoringEnabled = true;
      
      // Update status display
      document.getElementById('proctoring-status').innerHTML = `
        <div style="color: #28a745; margin-bottom: 0.5rem;">✅ Camera: Active</div>
        <div style="color: #28a745; margin-bottom: 0.5rem;">✅ Microphone: Active</div>
        <div style="color: #28a745;">✅ Screen monitoring: Active</div>
      `;
      
      alert('Proctoring enabled! Your exam session is now being monitored.');
      
      // TODO: Implement actual proctoring logic
      // - Face detection
      // - Audio monitoring
      // - Screen capture
      // - Suspicious activity detection
    })
    .catch(error => {
      console.error('Error accessing media devices:', error);
      alert('Unable to access camera/microphone. Please check your permissions and try again.');
    });
}

function saveProgress() {
  const answers = collectAnswers();
  localStorage.setItem('exam_{{ exam.id }}_progress', JSON.stringify({
    answers: answers,
    timestamp: new Date().toISOString()
  }));
  alert('Progress saved!');
}

function reviewAnswers() {
  const answers = collectAnswers();
  let review = 'Your current answers:\n\n';
  
  Object.keys(answers).forEach(questionNum => {
    review += `Question ${questionNum}: ${answers[questionNum]}\n`;
  });
  
  if (Object.keys(answers).length === 0) {
    review = 'No answers recorded yet.';
  }
  
  alert(review);
}

// Load saved progress on page load
window.addEventListener('load', function() {
  const savedProgress = localStorage.getItem('exam_{{ exam.id }}_progress');
  if (savedProgress) {
    try {
      const data = JSON.parse(savedProgress);
      // Restore answers
      Object.keys(data.answers).forEach(questionNum => {
        const answer = data.answers[questionNum];
        const radioInput = document.querySelector(`input[name="question_${questionNum}"][value="${answer}"]`);
        const textInput = document.querySelector(`textarea[name="question_${questionNum}"]`);
        
        if (radioInput) {
          radioInput.checked = true;
        } else if (textInput) {
          textInput.value = answer;
        }
      });
      updateProgress();
    } catch (e) {
      console.error('Error loading saved progress:', e);
    }
  }
});

// Prevent page refresh/close during exam
window.addEventListener('beforeunload', function(e) {
  if (examStarted) {
    e.preventDefault();
    e.returnValue = 'Are you sure you want to leave? Your exam progress may be lost.';
    return e.returnValue;
  }
});
</script>
{% endblock %}
