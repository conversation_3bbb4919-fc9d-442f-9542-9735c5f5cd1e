{% extends 'base.html' %}
{% block title %}Monitor Exam: {{ exam.title }} | AI Examiner{% endblock %}
{% block content %}
<div style="max-width: 1200px; margin: 0 auto; padding: 2rem;">
  <h2 style="margin-bottom: 2rem; color: #1a2233;">🔍 Monitor Exam: {{ exam.title }}</h2>
  
  <!-- Statistics Cards -->
  <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 2rem;">
    <div style="background: white; border: 1px solid #dee2e6; border-radius: 8px; padding: 1.5rem; text-align: center;">
      <div style="font-size: 2rem; color: #28a745; margin-bottom: 0.5rem;">{{ stats.active_sessions }}</div>
      <div style="font-weight: bold; color: #495057;">Active Sessions</div>
    </div>
    <div style="background: white; border: 1px solid #dee2e6; border-radius: 8px; padding: 1.5rem; text-align: center;">
      <div style="font-size: 2rem; color: #ffc107; margin-bottom: 0.5rem;">{{ stats.flagged_activities }}</div>
      <div style="font-weight: bold; color: #495057;">Flagged Activities</div>
    </div>
    <div style="background: white; border: 1px solid #dee2e6; border-radius: 8px; padding: 1.5rem; text-align: center;">
      <div style="font-size: 2rem; color: #dc3545; margin-bottom: 0.5rem;">{{ stats.violations }}</div>
      <div style="font-weight: bold; color: #495057;">Violations</div>
    </div>
    <div style="background: white; border: 1px solid #dee2e6; border-radius: 8px; padding: 1.5rem; text-align: center;">
      <div style="font-size: 2rem; color: #17a2b8; margin-bottom: 0.5rem;">{{ stats.total_participants }}</div>
      <div style="font-weight: bold; color: #495057;">Total Participants</div>
    </div>
  </div>

  <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
    <!-- Active Sessions -->
    <div style="background: white; border: 1px solid #dee2e6; border-radius: 8px; padding: 1.5rem;">
      <h3 style="margin-top: 0; margin-bottom: 1.5rem; color: #1a2233;">👥 Active Sessions</h3>
      {% if active_sessions %}
        {% for session in active_sessions %}
        <div style="border: 1px solid #e9ecef; border-radius: 8px; padding: 1rem; margin-bottom: 1rem; background: #f8f9fa;">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
              <strong>Student: Student {{ session.student_id }}</strong><br>
              <small style="color: #6c757d;">Started: {{ session.start_time.strftime('%H:%M:%S') if session.start_time else 'Unknown' }}</small><br>
              <small style="color: #6c757d;">Duration: Active</small>
            </div>
            <div style="display: flex; gap: 0.5rem;">
              <span style="background: #28a745; color: white; padding: 0.25rem 0.5rem; border-radius: 3px; font-size: 0.8rem;">✅ Camera</span>
              <span style="background: #28a745; color: white; padding: 0.25rem 0.5rem; border-radius: 3px; font-size: 0.8rem;">✅ Audio</span>
            </div>
          </div>
          <div style="margin-top: 0.5rem;">
            <div style="background: #e9ecef; border-radius: 4px; height: 8px; overflow: hidden;">
              <div style="background: #28a745; height: 100%; width: {{ session.trust_score or 100 }}%;"></div>
            </div>
            <small style="color: #6c757d;">Trust Score: {{ session.trust_score or 100 }}%</small>
          </div>
        </div>
        {% endfor %}
      {% else %}
        <div style="text-align: center; padding: 2rem; color: #6c757d;">
          <div style="font-size: 3rem; margin-bottom: 1rem;">📝</div>
          <h5>No Active Sessions</h5>
          <p>Students haven't started taking this exam yet.</p>
        </div>
      {% endif %}
    </div>

    <!-- Recent Events -->
    <div style="background: white; border: 1px solid #dee2e6; border-radius: 8px; padding: 1.5rem;">
      <h3 style="margin-top: 0; margin-bottom: 1.5rem; color: #1a2233;">🚨 Recent Events</h3>
      {% if recent_events %}
        {% for event in recent_events[:5] %}
        <div style="border: 1px solid {% if event.severity_score and event.severity_score > 0.8 %}#dc3545{% elif event.severity_score and event.severity_score > 0.5 %}#ffc107{% else %}#28a745{% endif %};
                    background: {% if event.severity_score and event.severity_score > 0.8 %}#f8d7da{% elif event.severity_score and event.severity_score > 0.5 %}#fff3cd{% else %}#d4edda{% endif %};
                    border-radius: 4px; padding: 1rem; margin-bottom: 1rem;">
          <div style="color: {% if event.severity_score and event.severity_score > 0.8 %}#721c24{% elif event.severity_score and event.severity_score > 0.5 %}#856404{% else %}#155724{% endif %};">
            <strong>
              {% if event.severity_score and event.severity_score > 0.8 %}🚨 Violation Detected
              {% elif event.severity_score and event.severity_score > 0.5 %}⚠️ Suspicious Activity
              {% else %}✅ Normal Activity
              {% endif %}
            </strong>
          </div>
          <div style="margin-top: 0.5rem;">
            <strong>Event:</strong> {{ event.event_type }}<br>
            <strong>Time:</strong> {{ event.timestamp.strftime('%H:%M:%S') }}<br>
            <strong>Severity:</strong> {{ (event.severity_score * 100)|round if event.severity_score else 0 }}%
          </div>
        </div>
        {% endfor %}
      {% else %}
        <div style="text-align: center; padding: 2rem; color: #6c757d;">
          <div style="font-size: 3rem; margin-bottom: 1rem;">✅</div>
          <h5>No Recent Events</h5>
          <p>All students are behaving normally.</p>
        </div>
      {% endif %}
    </div>
  </div>

  <!-- Control Panel -->
  <div style="background: white; border: 1px solid #dee2e6; border-radius: 8px; padding: 1.5rem; margin-top: 2rem;">
    <h3 style="margin-top: 0; margin-bottom: 1.5rem; color: #1a2233;">🎛️ Control Panel</h3>
    <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
      <button onclick="location.reload()" style="background: #007bff; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 4px; cursor: pointer; font-weight: bold;">
        🔄 Refresh Data
      </button>
      <button onclick="window.location.href='/api/exam/dashboard'" style="background: #6c757d; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 4px; cursor: pointer; font-weight: bold;">
        ← Back to Dashboard
      </button>
      <button onclick="window.location.href='/api/exam/details/{{ exam.id }}'" style="background: #17a2b8; color: white; border: none; padding: 0.75rem 1.5rem; border-radius: 4px; cursor: pointer; font-weight: bold;">
        📊 Exam Details
      </button>
    </div>
  </div>
</div>

<script>
// Auto-refresh every 30 seconds
setInterval(function() {
  location.reload();
}, 30000);

console.log('Monitoring page loaded successfully');
</script>
{% endblock %}
