{% extends 'base.html' %}
{% block title %}Exam Completed: {{ exam.title }} | AI Examiner{% endblock %}
{% block content %}
<div style="max-width: 800px; margin: 0 auto; text-align: center;">
  <div style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 3rem; margin-bottom: 2rem;">
    <h2 style="margin-top: 0; color: #155724;">✅ Exam Completed Successfully!</h2>
    <h3 style="color: #1a2233; margin-bottom: 2rem;">{{ exam.title }}</h3>
    
    <div style="background: white; border-radius: 8px; padding: 2rem; margin: 2rem 0;">
      <h4 style="margin-top: 0; color: #6c757d;">Your Results</h4>
      
      {% if submission.score is not none and submission.max_score is not none %}
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem; margin-bottom: 1.5rem;">
        <div style="background: #f8f9fa; padding: 1rem; border-radius: 4px;">
          <div style="font-size: 2rem; font-weight: bold; color: #28a745;">{{ submission.score }}</div>
          <div style="color: #6c757d;">Points Earned</div>
        </div>
        <div style="background: #f8f9fa; padding: 1rem; border-radius: 4px;">
          <div style="font-size: 2rem; font-weight: bold; color: #6c757d;">{{ submission.max_score }}</div>
          <div style="color: #6c757d;">Total Points</div>
        </div>
        <div style="background: #f8f9fa; padding: 1rem; border-radius: 4px;">
          <div style="font-size: 2rem; font-weight: bold; color: #007bff;">{{ "%.1f"|format((submission.score / submission.max_score * 100) if submission.max_score > 0 else 0) }}%</div>
          <div style="color: #6c757d;">Percentage</div>
        </div>
      </div>
      {% endif %}
      
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1.5rem;">
        <div>
          <strong>Submitted:</strong> {{ submission.submitted_at.strftime('%Y-%m-%d %H:%M:%S') }}
        </div>
        {% if submission.time_taken_minutes %}
        <div>
          <strong>Time Taken:</strong> {{ submission.time_taken_minutes }} minutes
        </div>
        {% endif %}
        <div>
          <strong>Status:</strong> {{ submission.status.title() }}
        </div>
      </div>
      
      {% if submission.score is not none and submission.max_score is not none %}
      <div style="margin-top: 1.5rem;">
        {% set percentage = (submission.score / submission.max_score * 100) if submission.max_score > 0 else 0 %}
        {% if percentage >= 90 %}
          <div style="color: #28a745; font-weight: bold;">🎉 Excellent work!</div>
        {% elif percentage >= 80 %}
          <div style="color: #28a745; font-weight: bold;">👍 Great job!</div>
        {% elif percentage >= 70 %}
          <div style="color: #ffc107; font-weight: bold;">👌 Good effort!</div>
        {% elif percentage >= 60 %}
          <div style="color: #fd7e14; font-weight: bold;">📚 Keep studying!</div>
        {% else %}
          <div style="color: #dc3545; font-weight: bold;">💪 Don't give up!</div>
        {% endif %}
      </div>
      {% endif %}
    </div>
    
    <div style="margin-top: 2rem;">
      <a href="/api/exam/dashboard" style="background: #007bff; color: white; text-decoration: none; padding: 1rem 2rem; border-radius: 4px; display: inline-block; font-weight: bold;">
        🏠 Back to Dashboard
      </a>
    </div>
  </div>
  
  <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 1.5rem; text-align: left;">
    <h4 style="margin-top: 0; color: #6c757d;">📋 Exam Information</h4>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
      <div>
        <strong>Duration:</strong> {{ exam.duration_minutes }} minutes
      </div>
      <div>
        <strong>Questions:</strong> {{ submission.answers|length if submission.answers else 'N/A' }}
      </div>
      <div>
        <strong>Question Types:</strong> {{ exam.question_types or 'Mixed' }}
      </div>
    </div>
    {% if exam.instructions %}
    <div style="margin-top: 1rem; padding: 1rem; background: #e9ecef; border-radius: 4px;">
      <strong>Instructions:</strong><br>
      {{ exam.instructions }}
    </div>
    {% endif %}
  </div>
</div>
{% endblock %}
