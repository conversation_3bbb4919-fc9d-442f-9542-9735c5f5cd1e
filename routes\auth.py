
from flask import Blueprint, request, jsonify, session, render_template, redirect, url_for
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from models import User, db
import base64

auth_bp = Blueprint('auth', __name__)

# Render login page
@auth_bp.route('/login', methods=['GET'])
def login_page():
    return render_template('auth/login.html')

# Render register page
@auth_bp.route('/register', methods=['GET'])
def register_page():
    return render_template('auth/register.html')

# Login API (form or JSON)
@auth_bp.route('/login', methods=['POST'])
def login():
    data = request.get_json(silent=True) or request.form
    username = data.get('username')
    password = data.get('password')
    user = User.query.filter_by(username=username).first()
    if user and user.check_password(password):
        login_user(user)
        if request.is_json:
            return jsonify({'message': 'Login successful', 'role': user.role})
        return redirect(url_for('exam.dashboard'))
    if request.is_json:
        return jsonify({'error': 'Invalid credentials'}), 401
    return render_template('auth/login.html', error='Invalid credentials')

# Register API (form or JSON)
@auth_bp.route('/register', methods=['POST'])
def register():
    data = request.get_json(silent=True) or request.form
    username = data.get('username')
    password = data.get('password')
    role = data.get('role')
    face_encoding = data.get('face_encoding')  # Should be bytes
    if not username or not password or not role:
        if request.is_json:
            return jsonify({'error': 'Missing fields'}), 400
        return render_template('auth/register.html', error='Missing fields')
    if User.query.filter_by(username=username).first():
        if request.is_json:
            return jsonify({'error': 'Username already exists'}), 409
        return render_template('auth/register.html', error='Username already exists')
    # Handle face encoding - convert base64 to binary if present
    face_data = None
    if face_encoding:
        try:
            # Remove data URL prefix if present
            if face_encoding.startswith('data:image'):
                face_encoding = face_encoding.split(',')[1]
            # Convert base64 to binary
            face_data = base64.b64decode(face_encoding)
        except Exception as e:
            print(f"Error processing face encoding: {e}")
            face_data = None

    user = User()
    user.username = username # pyright: ignore[reportAttributeAccessIssue]
    user.role = role # pyright: ignore[reportAttributeAccessIssue]
    user.set_password(password)
    if face_data:
        user.face_encoding = face_data
    db.session.add(user)
    db.session.commit()
    if request.is_json:
        return jsonify({'message': 'User registered successfully'})
    return redirect(url_for('auth.login'))

# Allow GET and POST logout for template compatibility
@auth_bp.route('/logout', methods=['GET', 'POST'])
@login_required
def logout():
    logout_user()
    return redirect(url_for('auth.login'))
