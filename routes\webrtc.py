from flask import Blueprint, request, jsonify, current_app

from aiortc import RTCPeerConnection, RTCSessionDescription, RTCConfiguration, RTCIceServer
import asyncio
from ai_modules.analysis_pipeline import process_streams
from av import VideoFrame

webrtc_bp = Blueprint('webrtc', __name__)

pcs = {}

def _pc_config():
    servers = []
    stun = current_app.config.get('WEBRTC_STUN_SERVER')
    turn = current_app.config.get('WEBRTC_TURN_SERVER')
    if stun: servers.append(RTCIceServer(urls=[stun]))
    if turn: servers.append(RTCIceServer(urls=[turn]))
    return RTCConfiguration(servers)

@webrtc_bp.route('/offer', methods=['POST'])
def offer():
    data = request.get_json()
    pc = RTCPeerConnection(_pc_config())
    session_id = data.get('session_id')
    pcs[id(pc)] = {'pc': pc, 'session_id': session_id}
    offer = RTCSessionDescription(sdp=data['sdp'], type=data['type'])

    async def _run():
        await pc.setRemoteDescription(offer)
        pc.addTransceiver('video', direction='recvonly')
        pc.addTransceiver('audio', direction='recvonly')
        pc_entry = pcs[id(pc)]

        @pc.on('track')
        async def on_track(track):
            if track.kind == 'video':
                async def video_iter():
                    while True:
                        frame = await track.recv()
                        yield frame
                pc_entry['video_iter'] = video_iter()
            elif track.kind == 'audio':
                async def audio_iter():
                    while True:
                        frame = await track.recv()
                        yield frame
                pc_entry['audio_iter'] = audio_iter()

            # When both iterators are ready, start processing in a background task
            if 'video_iter' in pc_entry and 'audio_iter' in pc_entry and not pc_entry.get('started'):
                pc_entry['started'] = True
                session_id = pc_entry['session_id']
                loop = asyncio.get_event_loop()
                async def run_pipeline():
                    await asyncio.to_thread(process_streams, session_id, pc_entry['video_iter'], pc_entry['audio_iter'])
                loop.create_task(run_pipeline())

        answer = await pc.createAnswer()
        await pc.setLocalDescription(answer)
        return {'sdp': pc.localDescription.sdp, 'type': pc.localDescription.type, 'pc_id': id(pc)}
    return jsonify(asyncio.get_event_loop().run_until_complete(_run()))

@webrtc_bp.route('/candidate', methods=['POST'])
def candidate():
    data = request.get_json()
    pc_entry = pcs.get(data['pc_id'])
    if not pc_entry: return ('Not Found', 404)
    pc = pc_entry['pc']
    cand = data['candidate']
    async def _run():
        await pc.addIceCandidate(cand)
    asyncio.get_event_loop().run_until_complete(_run())
    return ('', 204)
