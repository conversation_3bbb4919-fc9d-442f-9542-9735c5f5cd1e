{% extends 'base.html' %}
{% block title %}Monitor Exam: {{ exam.title }} | AI Examiner{% endblock %}
{% block content %}
<div style="max-width: 1400px; margin: 0 auto;">
  <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
    <h2>Monitoring: {{ exam.title }}</h2>
    <div>
      <a href="/api/exam/details/{{ exam.id }}" style="background: #6c757d; border: none; color: white; padding: 0.5rem 1rem; border-radius: 4px; text-decoration: none; margin-right: 0.5rem;">
        📋 Exam Details
      </a>
      <a href="/api/exam/dashboard" style="background: #6c757d; border: none; color: white; padding: 0.5rem 1rem; border-radius: 4px; text-decoration: none;">
        ← Back to Dashboard
      </a>
    </div>
  </div>

  <!-- Real-time Status -->
  <div style="background: white; border: 1px solid #dee2e6; border-radius: 8px; padding: 1.5rem; margin-bottom: 2rem;">
    <h4 style="margin-top: 0; color: #495057;">🔴 Live Monitoring Status</h4>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
      <div style="background: #d4edda; border: 1px solid #c3e6cb; padding: 1rem; border-radius: 4px; text-align: center;">
        <div id="active-sessions-count" style="font-size: 1.5rem; font-weight: bold; color: #155724;">{{ stats.active_sessions if stats else 0 }}</div>
        <div style="color: #155724;">Active Sessions</div>
      </div>
      <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 1rem; border-radius: 4px; text-align: center;">
        <div id="flagged-activities-count" style="font-size: 1.5rem; font-weight: bold; color: #856404;">{{ stats.flagged_activities if stats else 0 }}</div>
        <div style="color: #856404;">Flagged Activities</div>
      </div>
      <div style="background: #f8d7da; border: 1px solid #f5c6cb; padding: 1rem; border-radius: 4px; text-align: center;">
        <div id="violations-count" style="font-size: 1.5rem; font-weight: bold; color: #721c24;">{{ stats.violations if stats else 0 }}</div>
        <div style="color: #721c24;">Violations Detected</div>
      </div>
      <div style="background: #d1ecf1; border: 1px solid #bee5eb; padding: 1rem; border-radius: 4px; text-align: center;">
        <div id="total-participants-count" style="font-size: 1.5rem; font-weight: bold; color: #0c5460;">{{ stats.total_participants if stats else 0 }}</div>
        <div style="color: #0c5460;">Total Participants</div>
      </div>
    </div>
  </div>

  <div style="display: grid; grid-template-columns: 1fr 400px; gap: 2rem;">
    <!-- Main Monitoring Area -->
    <div>
      <!-- Active Sessions -->
      <div style="background: white; border: 1px solid #dee2e6; border-radius: 8px; padding: 1.5rem; margin-bottom: 2rem;">
        <h4 style="margin-top: 0; color: #495057;">👥 Active Exam Sessions</h4>
        <div id="active-sessions">
          {% if active_sessions %}
            {% for session in active_sessions %}
            <div style="border: 1px solid #dee2e6; border-radius: 4px; padding: 1rem; margin-bottom: 1rem;">
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <div>
                  <strong>Student: {{ session.student.username if session.student else 'Unknown' }}</strong><br>
                  <small style="color: #6c757d;">Started: {{ session.start_time.strftime('%H:%M:%S') if session.start_time else 'Unknown' }}</small><br>
                  <small style="color: #6c757d;">Duration: Active</small>
                </div>
                <div style="display: flex; gap: 0.5rem;">
                  <span style="background: #28a745; color: white; padding: 0.25rem 0.5rem; border-radius: 3px; font-size: 0.8rem;">✅ Camera</span>
                  <span style="background: #28a745; color: white; padding: 0.25rem 0.5rem; border-radius: 3px; font-size: 0.8rem;">✅ Audio</span>
                  {% if session.trust_score and session.trust_score < 80 %}
                  <span style="background: #dc3545; color: white; padding: 0.25rem 0.5rem; border-radius: 3px; font-size: 0.8rem;">⚠️ Low Trust</span>
                  {% endif %}
                </div>
              </div>
              <div style="margin-top: 0.5rem;">
                <button onclick="viewStudent({{ session.id }})" style="background: #007bff; border: none; color: white; padding: 0.25rem 0.5rem; border-radius: 3px; font-size: 0.8rem; cursor: pointer;">
                  👁️ View Details
                </button>
                <span style="margin-left: 1rem; color: #6c757d; font-size: 0.8rem;">
                  Trust Score: {{ session.trust_score or 100 }}%
                </span>
              </div>
            </div>
            {% endfor %}
          {% else %}
            <div style="text-align: center; padding: 2rem; color: #6c757d;">
              <div style="font-size: 3rem; margin-bottom: 1rem;">📝</div>
              <h5>No Active Sessions</h5>
              <p>Students haven't started taking this exam yet.</p>
            </div>
          {% endif %}
          <div style="text-align: center; margin-top: 1rem;">
            <button onclick="refreshSessions()" style="background: #007bff; border: none; color: white; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer;">
              🔄 Refresh
            </button>
          </div>
        </div>
      </div>

      <!-- Proctoring Alerts -->
      <div style="background: white; border: 1px solid #dee2e6; border-radius: 8px; padding: 1.5rem;">
        <h4 style="margin-top: 0; color: #495057;">🚨 Proctoring Alerts</h4>
        <div id="proctoring-alerts">
          {% if recent_events %}
            {% for event in recent_events[:5] %}
            <div style="border: 1px solid {% if event.severity_score and event.severity_score > 0.8 %}#dc3545{% elif event.severity_score and event.severity_score > 0.5 %}#ffc107{% else %}#28a745{% endif %};
                        background: {% if event.severity_score and event.severity_score > 0.8 %}#f8d7da{% elif event.severity_score and event.severity_score > 0.5 %}#fff3cd{% else %}#d4edda{% endif %};
                        border-radius: 4px; padding: 1rem; margin-bottom: 1rem;">
              <div style="color: {% if event.severity_score and event.severity_score > 0.8 %}#721c24{% elif event.severity_score and event.severity_score > 0.5 %}#856404{% else %}#155724{% endif %};">
                <strong>
                  {% if event.severity_score and event.severity_score > 0.8 %}🚨 Violation Detected
                  {% elif event.severity_score and event.severity_score > 0.5 %}⚠️ Suspicious Activity
                  {% else %}ℹ️ Normal Activity
                  {% endif %}
                </strong><br>
                <small>Event: {{ event.event_type.replace('_', ' ').title() }}</small><br>
                <small>Time: {{ event.timestamp.strftime('%H:%M:%S') if event.timestamp else 'Unknown' }}</small><br>
                <small>Severity: {{ (event.severity_score * 100) | round if event.severity_score else 0 }}%</small>
              </div>
            </div>
            {% endfor %}
          {% else %}
            <div style="text-align: center; padding: 2rem; color: #6c757d;">
              <div style="font-size: 3rem; margin-bottom: 1rem;">✅</div>
              <h5>All Clear</h5>
              <p>No suspicious activities detected.</p>
            </div>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- Sidebar Controls -->
    <div>
      <!-- Exam Controls -->
      <div style="background: white; border: 1px solid #dee2e6; border-radius: 8px; padding: 1.5rem; margin-bottom: 1rem;">
        <h4 style="margin-top: 0; color: #495057;">⚙️ Exam Controls</h4>
        <div style="display: flex; flex-direction: column; gap: 0.5rem;">
          <button onclick="startMonitoring()" id="start-monitoring" style="background: #28a745; border: none; color: white; padding: 0.75rem; border-radius: 4px; cursor: pointer;">
            ▶️ Start Monitoring
          </button>
          <button onclick="pauseExam()" style="background: #ffc107; border: none; color: #212529; padding: 0.75rem; border-radius: 4px; cursor: pointer;">
            ⏸️ Pause Exam
          </button>
          <button onclick="endExam()" style="background: #dc3545; border: none; color: white; padding: 0.75rem; border-radius: 4px; cursor: pointer;">
            ⏹️ End Exam
          </button>
        </div>
      </div>

      <!-- Monitoring Settings -->
      <div style="background: white; border: 1px solid #dee2e6; border-radius: 8px; padding: 1.5rem; margin-bottom: 1rem;">
        <h4 style="margin-top: 0; color: #495057;">🔧 Monitoring Settings</h4>
        <div style="margin-bottom: 1rem;">
          <label style="display: flex; align-items: center; margin-bottom: 0.5rem;">
            <input type="checkbox" checked style="margin-right: 0.5rem;">
            Face Detection
          </label>
          <label style="display: flex; align-items: center; margin-bottom: 0.5rem;">
            <input type="checkbox" checked style="margin-right: 0.5rem;">
            Audio Monitoring
          </label>
          <label style="display: flex; align-items: center; margin-bottom: 0.5rem;">
            <input type="checkbox" checked style="margin-right: 0.5rem;">
            Screen Sharing Detection
          </label>
          <label style="display: flex; align-items: center; margin-bottom: 0.5rem;">
            <input type="checkbox" checked style="margin-right: 0.5rem;">
            Tab Switch Detection
          </label>
        </div>
        <button onclick="saveSettings()" style="background: #007bff; border: none; color: white; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer; width: 100%;">
          💾 Save Settings
        </button>
      </div>

      <!-- Quick Actions -->
      <div style="background: white; border: 1px solid #dee2e6; border-radius: 8px; padding: 1.5rem;">
        <h4 style="margin-top: 0; color: #495057;">⚡ Quick Actions</h4>
        <div style="display: flex; flex-direction: column; gap: 0.5rem;">
          <button onclick="exportResults()" style="background: #17a2b8; border: none; color: white; padding: 0.5rem; border-radius: 4px; cursor: pointer;">
            📊 Export Results
          </button>
          <button onclick="sendMessage()" style="background: #6f42c1; border: none; color: white; padding: 0.5rem; border-radius: 4px; cursor: pointer;">
            💬 Send Message
          </button>
          <button onclick="viewLogs()" style="background: #6c757d; border: none; color: white; padding: 0.5rem; border-radius: 4px; cursor: pointer;">
            📋 View Logs
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
let monitoringActive = false;
let refreshInterval;

function startMonitoring() {
  if (monitoringActive) {
    // Stop monitoring
    monitoringActive = false;
    if (refreshInterval) clearInterval(refreshInterval);
    document.getElementById('start-monitoring').textContent = '▶️ Start Monitoring';
    document.getElementById('start-monitoring').style.background = '#28a745';
    alert('Monitoring stopped.');
    return;
  }

  monitoringActive = true;
  document.getElementById('start-monitoring').textContent = '⏹️ Stop Monitoring';
  document.getElementById('start-monitoring').style.background = '#dc3545';

  // Start auto-refresh every 5 seconds
  refreshInterval = setInterval(refreshSessions, 5000);

  // Initial refresh
  refreshSessions();

  alert('Real-time monitoring started! Data will refresh every 5 seconds.');
}

function refreshSessions() {
  // Show real-time monitoring data
  console.log('Refreshing monitoring data...');

  // Update statistics with current data
  const stats = {
    active_sessions: {{ active_sessions|length }},
    flagged_activities: 0,
    violations: 0,
    total_participants: {{ active_sessions|length }}
  };

  updateStatistics(stats);

  // Show current active sessions
  {% if active_sessions %}
  const sessionsData = [
    {% for session in active_sessions %}
    {
      session_id: {{ session.id }},
      student_name: '{{ session.student.username }}',
      start_time: '{{ session.start_time.strftime('%H:%M:%S') if session.start_time else 'Unknown' }}',
      duration: 'Active',
      trust_score: {{ session.trust_score or 100 }},
      has_camera: true,
      has_audio: true,
      violations_count: 0,
      warnings_count: 0,
      latest_event: 'exam_active'
    }{% if not loop.last %},{% endif %}
    {% endfor %}
  ];
  updateActiveSessions(sessionsData);
  {% else %}
  updateActiveSessions([]);
  {% endif %}

  // Show recent events
  {% if recent_events %}
  const eventsData = [
    {% for event in recent_events[:10] %}
    {
      id: {{ event.id }},
      student_name: 'Student',
      event_type: '{{ event.event_type }}',
      timestamp: '{{ event.timestamp.strftime('%H:%M:%S') }}',
      severity_score: {{ event.severity_score or 0 }},
      metadata: {}
    }{% if not loop.last %},{% endif %}
    {% endfor %}
  ];
  updateProctoringAlerts(eventsData);
  {% else %}
  updateProctoringAlerts([]);
  {% endif %}
}

function updateStatistics(stats) {
  document.getElementById('active-sessions-count').textContent = stats.active_sessions;
  document.getElementById('flagged-activities-count').textContent = stats.flagged_activities;
  document.getElementById('violations-count').textContent = stats.violations;
  document.getElementById('total-participants-count').textContent = stats.total_participants;
}

function updateActiveSessions(sessions) {
  const sessionsDiv = document.getElementById('active-sessions');

  if (sessions.length === 0) {
    sessionsDiv.innerHTML = `
      <div style="text-align: center; padding: 2rem; color: #6c757d;">
        <div style="font-size: 3rem; margin-bottom: 1rem;">📝</div>
        <h5>No Active Sessions</h5>
        <p>Students haven't started taking this exam yet.</p>
      </div>
      <div style="text-align: center; margin-top: 1rem;">
        <button onclick="refreshSessions()" style="background: #007bff; border: none; color: white; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer;">
          🔄 Refresh
        </button>
      </div>
    `;
    return;
  }

  let html = '';
  sessions.forEach(session => {
    const trustColor = session.trust_score >= 80 ? '#28a745' : session.trust_score >= 60 ? '#ffc107' : '#dc3545';
    const cameraStatus = session.has_camera ? '✅ Camera' : '❌ Camera';
    const audioStatus = session.has_audio ? '✅ Audio' : '❌ Audio';

    html += `
      <div style="border: 1px solid #dee2e6; border-radius: 4px; padding: 1rem; margin-bottom: 1rem;">
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <div>
            <strong>Student: ${session.student_name}</strong><br>
            <small style="color: #6c757d;">Started: ${session.start_time}</small><br>
            <small style="color: #6c757d;">Duration: ${session.duration}</small>
          </div>
          <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
            <span style="background: ${session.has_camera ? '#28a745' : '#dc3545'}; color: white; padding: 0.25rem 0.5rem; border-radius: 3px; font-size: 0.8rem;">${cameraStatus}</span>
            <span style="background: ${session.has_audio ? '#28a745' : '#dc3545'}; color: white; padding: 0.25rem 0.5rem; border-radius: 3px; font-size: 0.8rem;">${audioStatus}</span>
            ${session.violations_count > 0 ? `<span style="background: #dc3545; color: white; padding: 0.25rem 0.5rem; border-radius: 3px; font-size: 0.8rem;">🚨 ${session.violations_count} Violations</span>` : ''}
            ${session.warnings_count > 0 ? `<span style="background: #ffc107; color: #212529; padding: 0.25rem 0.5rem; border-radius: 3px; font-size: 0.8rem;">⚠️ ${session.warnings_count} Warnings</span>` : ''}
          </div>
        </div>
        <div style="margin-top: 0.5rem;">
          <button onclick="viewStudent(${session.session_id})" style="background: #007bff; border: none; color: white; padding: 0.25rem 0.5rem; border-radius: 3px; font-size: 0.8rem; cursor: pointer;">
            👁️ View Details
          </button>
          <span style="margin-left: 1rem; color: ${trustColor}; font-size: 0.8rem; font-weight: bold;">
            Trust Score: ${session.trust_score}%
          </span>
        </div>
      </div>
    `;
  });

  html += `
    <div style="text-align: center; margin-top: 1rem;">
      <button onclick="refreshSessions()" style="background: #007bff; border: none; color: white; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer;">
        🔄 Refresh
      </button>
    </div>
  `;

  sessionsDiv.innerHTML = html;
}

function updateProctoringAlerts(events) {
  const alertsDiv = document.getElementById('proctoring-alerts');

  if (events.length === 0) {
    alertsDiv.innerHTML = `
      <div style="text-align: center; padding: 2rem; color: #6c757d;">
        <div style="font-size: 3rem; margin-bottom: 1rem;">✅</div>
        <h5>All Clear</h5>
        <p>No suspicious activities detected.</p>
      </div>
    `;
    return;
  }

  let html = '';
  events.slice(0, 5).forEach(event => {
    const severity = event.severity_score;
    let borderColor, bgColor, textColor, icon, title;

    if (severity > 0.8) {
      borderColor = '#dc3545';
      bgColor = '#f8d7da';
      textColor = '#721c24';
      icon = '🚨';
      title = 'Violation Detected';
    } else if (severity > 0.5) {
      borderColor = '#ffc107';
      bgColor = '#fff3cd';
      textColor = '#856404';
      icon = '⚠️';
      title = 'Suspicious Activity';
    } else {
      borderColor = '#28a745';
      bgColor = '#d4edda';
      textColor = '#155724';
      icon = 'ℹ️';
      title = 'Normal Activity';
    }

    html += `
      <div style="border: 1px solid ${borderColor}; background: ${bgColor}; border-radius: 4px; padding: 1rem; margin-bottom: 1rem;">
        <div style="color: ${textColor};">
          <strong>${icon} ${title}</strong><br>
          <small>Student: ${event.student_name}</small><br>
          <small>Event: ${event.event_type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</small><br>
          <small>Time: ${event.timestamp}</small><br>
          <small>Severity: ${Math.round(severity * 100)}%</small>
        </div>
      </div>
    `;
  });

  alertsDiv.innerHTML = html;
}

function pauseExam() {
  if (confirm('Are you sure you want to pause this exam for all participants?')) {
    // TODO: Implement pause functionality
    alert('Exam paused for all participants. They will be notified.');
  }
}

function endExam() {
  if (confirm('Are you sure you want to end this exam for all participants?\n\nThis will:\n- Force submit all active sessions\n- End the exam for everyone\n- This action cannot be undone')) {
    // TODO: Implement end exam functionality
    monitoringActive = false;
    if (refreshInterval) clearInterval(refreshInterval);
    alert('Exam ended. All participants have been notified and their answers submitted.');
  }
}

function saveSettings() {
  // Get all checkbox states
  const settings = {
    face_detection: document.querySelector('input[type="checkbox"]:nth-of-type(1)').checked,
    audio_monitoring: document.querySelector('input[type="checkbox"]:nth-of-type(2)').checked,
    screen_sharing_detection: document.querySelector('input[type="checkbox"]:nth-of-type(3)').checked,
    tab_switch_detection: document.querySelector('input[type="checkbox"]:nth-of-type(4)').checked
  };

  // TODO: Send settings to server
  console.log('Saving settings:', settings);
  alert('Monitoring settings saved successfully!');
}

function exportResults() {
  // TODO: Implement export functionality
  alert('Exam results will be exported to CSV format.');
}

function sendMessage() {
  const message = prompt('Enter message to send to all participants:');
  if (message && message.trim()) {
    // TODO: Implement message sending
    alert('Message sent to all active participants: "' + message + '"');
  }
}

function viewLogs() {
  // TODO: Implement logs view
  alert('Detailed monitoring logs will be displayed here.');
}

function viewStudent(sessionId) {
  // Fetch detailed student information
  fetch(`/api/exam/monitor/student/${sessionId}`)
    .then(response => response.json())
    .then(data => {
      if (data.error) {
        alert('Error: ' + data.error);
        return;
      }

      // Create a detailed view modal or new window
      showStudentDetails(data);
    })
    .catch(error => {
      console.error('Error fetching student details:', error);
      alert('Error fetching student details. Please try again.');
    });
}

function showStudentDetails(data) {
  const session = data.session;
  const events = data.events;

  // Create a modal-like display
  const modal = document.createElement('div');
  modal.style.cssText = `
    position: fixed; top: 0; left: 0; width: 100%; height: 100%;
    background: rgba(0,0,0,0.5); z-index: 1000; display: flex;
    align-items: center; justify-content: center;
  `;

  const content = document.createElement('div');
  content.style.cssText = `
    background: white; border-radius: 8px; padding: 2rem;
    max-width: 800px; max-height: 80vh; overflow-y: auto;
    width: 90%; position: relative;
  `;

  content.innerHTML = `
    <button onclick="this.closest('.modal').remove()" style="position: absolute; top: 1rem; right: 1rem; background: #dc3545; border: none; color: white; border-radius: 50%; width: 30px; height: 30px; cursor: pointer;">×</button>

    <h3>Student Monitoring Details</h3>

    <div style="background: #f8f9fa; padding: 1rem; border-radius: 4px; margin-bottom: 1rem;">
      <h4>Session Information</h4>
      <p><strong>Student:</strong> ${session.student_name} (${session.student_email})</p>
      <p><strong>Exam:</strong> ${session.exam_title}</p>
      <p><strong>Start Time:</strong> ${session.start_time}</p>
      <p><strong>Duration:</strong> ${session.duration}</p>
      <p><strong>Trust Score:</strong> <span style="color: ${session.trust_score >= 80 ? '#28a745' : session.trust_score >= 60 ? '#ffc107' : '#dc3545'}; font-weight: bold;">${session.trust_score}%</span></p>
      <p><strong>Violations:</strong> ${session.violations_count}</p>
      <p><strong>Warnings:</strong> ${session.warnings_count}</p>
      <p><strong>Total Events:</strong> ${session.total_events}</p>
    </div>

    <div style="background: #f8f9fa; padding: 1rem; border-radius: 4px; margin-bottom: 1rem;">
      <h4>Recent Events (Last 10)</h4>
      <div style="max-height: 300px; overflow-y: auto;">
        ${events.slice(0, 10).map(event => `
          <div style="border-bottom: 1px solid #dee2e6; padding: 0.5rem 0;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <span><strong>${event.event_type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</strong></span>
              <span style="color: ${event.severity_score > 0.8 ? '#dc3545' : event.severity_score > 0.5 ? '#ffc107' : '#28a745'}; font-weight: bold;">
                ${Math.round(event.severity_score * 100)}%
              </span>
            </div>
            <small style="color: #6c757d;">${event.timestamp}</small>
          </div>
        `).join('')}
      </div>
    </div>

    <div style="text-align: center;">
      <button onclick="this.closest('.modal').remove()" style="background: #6c757d; border: none; color: white; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer;">
        Close
      </button>
    </div>
  `;

  modal.className = 'modal';
  modal.appendChild(content);
  document.body.appendChild(modal);

  // Close modal when clicking outside
  modal.addEventListener('click', function(e) {
    if (e.target === modal) {
      modal.remove();
    }
  });
}

// Auto-refresh when monitoring is active
setInterval(() => {
  if (monitoringActive && !document.hidden) {
    refreshSessions();
  }
}, 10000); // Refresh every 10 seconds

// Initial load - ready for manual monitoring start
</script>
{% endblock %}
