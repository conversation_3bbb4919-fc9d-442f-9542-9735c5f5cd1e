-- 001_initial_schema.sql: Initial DB schema for AI Examiner
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(64) UNIQUE NOT NULL,
    password_hash VARCHAR(128) NOT NULL,
    role VARCHAR(16) NOT NULL,
    face_encoding BYTEA
);

CREATE TABLE exams (
    id SERIAL PRIMARY KEY,
    title VARCHAR(128) NOT NULL,
    duration_minutes INTEGER NOT NULL,
    created_by_id INTEGER REFERENCES users(id) NOT NULL
);

CREATE TABLE exam_sessions (
    id SERIAL PRIMARY KEY,
    student_id INTEGER REFERENCES users(id) NOT NULL,
    exam_id INTEGER REFERENCES exams(id) NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP,
    trust_score FLOAT,
    video_archive VARCHAR(256),
    audio_archive VARCHAR(256),
    screen_archive VARCHAR(256)
);


CREATE TABLE proctoring_events (
    id SERIAL PRIMARY KEY,
    session_id INTEGER REFERENCES exam_sessions(id) NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    event_type VARCHAR(64) NOT NULL,
    severity_score FLOAT,
    metadata JSONB
);

CREATE INDEX idx_proctoring_events_session_id ON proctoring_events(session_id);
CREATE INDEX idx_proctoring_events_timestamp ON proctoring_events(timestamp);

CREATE TABLE evidence (
    id SERIAL PRIMARY KEY,
    event_id INTEGER REFERENCES proctoring_events(id) NOT NULL,
    media_type VARCHAR(32) NOT NULL,
    file_path VARCHAR(256) NOT NULL
);
