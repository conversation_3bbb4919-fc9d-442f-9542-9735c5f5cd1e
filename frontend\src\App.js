import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Dashboard from './components/Dashboard';
import StudentDetailView from './components/StudentDetailView';
import AlertsPanel from './components/AlertsPanel';
import SessionReview from './components/SessionReview';

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/student/:id" element={<StudentDetailView />} />
        <Route path="/alerts" element={<AlertsPanel />} />
        <Route path="/session/:id/review" element={<SessionReview />} />
        <Route path="*" element={<Navigate to="/dashboard" />} />
      </Routes>
    </Router>
  );
}

export default App;
