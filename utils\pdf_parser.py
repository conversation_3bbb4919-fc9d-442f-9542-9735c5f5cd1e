import PyPDF2
import re
from typing import List, Dict, Tuple
import json

class PDFQuestionParser:
    """
    Parses PDF files to extract questions and their options
    """
    
    def __init__(self):
        # Common patterns for multiple choice questions
        self.mc_patterns = [
            r'(\d+)\.\s*(.+?)\n\s*[aA]\)\s*(.+?)\n\s*[bB]\)\s*(.+?)\n\s*[cC]\)\s*(.+?)\n\s*[dD]\)\s*(.+?)(?=\n\d+\.|\n[aA]\)|\Z)',
            r'(\d+)\.\s*(.+?)\n\s*[aA]\.\s*(.+?)\n\s*[bB]\.\s*(.+?)\n\s*[cC]\.\s*(.+?)\n\s*[dD]\.\s*(.+?)(?=\n\d+\.|\n[aA]\.|\Z)',
            r'(\d+)\)\s*(.+?)\n\s*[aA]\)\s*(.+?)\n\s*[bB]\)\s*(.+?)\n\s*[cC]\)\s*(.+?)\n\s*[dD]\)\s*(.+?)(?=\n\d+\)|\n[aA]\)|\Z)',
        ]
        
        # Patterns for true/false questions
        self.tf_patterns = [
            r'(\d+)\.\s*(.+?)\s*\(True/False\)',
            r'(\d+)\.\s*(.+?)\s*\(T/F\)',
        ]
        
        # Patterns for short answer questions
        self.sa_patterns = [
            r'(\d+)\.\s*(.+?)\s*\(Short Answer\)',
            r'(\d+)\.\s*(.+?)\s*_+',
        ]

    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """Extract text content from PDF file"""
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
                return text
        except Exception as e:
            print(f"Error reading PDF: {e}")
            return ""

    def parse_multiple_choice_questions(self, text: str) -> List[Dict]:
        """Parse multiple choice questions from text"""
        questions = []
        
        for pattern in self.mc_patterns:
            matches = re.finditer(pattern, text, re.DOTALL | re.IGNORECASE)
            for match in matches:
                question_num = int(match.group(1))
                question_text = match.group(2).strip()
                option_a = match.group(3).strip()
                option_b = match.group(4).strip()
                option_c = match.group(5).strip()
                option_d = match.group(6).strip()
                
                questions.append({
                    'question_number': question_num,
                    'question_text': question_text,
                    'question_type': 'multiple_choice',
                    'options': {
                        'A': option_a,
                        'B': option_b,
                        'C': option_c,
                        'D': option_d
                    },
                    'points': 1
                })
        
        return questions

    def parse_true_false_questions(self, text: str) -> List[Dict]:
        """Parse true/false questions from text"""
        questions = []
        
        for pattern in self.tf_patterns:
            matches = re.finditer(pattern, text, re.DOTALL | re.IGNORECASE)
            for match in matches:
                question_num = int(match.group(1))
                question_text = match.group(2).strip()
                
                questions.append({
                    'question_number': question_num,
                    'question_text': question_text,
                    'question_type': 'true_false',
                    'options': {
                        'True': 'True',
                        'False': 'False'
                    },
                    'points': 1
                })
        
        return questions

    def parse_short_answer_questions(self, text: str) -> List[Dict]:
        """Parse short answer questions from text"""
        questions = []
        
        for pattern in self.sa_patterns:
            matches = re.finditer(pattern, text, re.DOTALL | re.IGNORECASE)
            for match in matches:
                question_num = int(match.group(1))
                question_text = match.group(2).strip()
                
                questions.append({
                    'question_number': question_num,
                    'question_text': question_text,
                    'question_type': 'short_answer',
                    'options': None,
                    'points': 2
                })
        
        return questions

    def parse_pdf_questions(self, pdf_path: str) -> List[Dict]:
        """
        Main method to parse all questions from a PDF
        Returns a list of question dictionaries
        """
        text = self.extract_text_from_pdf(pdf_path)
        if not text:
            return []
        
        all_questions = []
        
        # Parse different question types
        mc_questions = self.parse_multiple_choice_questions(text)
        tf_questions = self.parse_true_false_questions(text)
        sa_questions = self.parse_short_answer_questions(text)
        
        all_questions.extend(mc_questions)
        all_questions.extend(tf_questions)
        all_questions.extend(sa_questions)
        
        # Sort by question number
        all_questions.sort(key=lambda x: x['question_number'])
        
        return all_questions

    def create_sample_questions(self, exam_id: int) -> List[Dict]:
        """
        Create sample questions for testing when PDF parsing fails
        """
        return [
            {
                'question_number': 1,
                'question_text': 'What is the primary factor that influences the efficiency of a system in achieving its objectives?',
                'question_type': 'multiple_choice',
                'options': {
                    'A': 'The alignment of its components towards a common goal',
                    'B': 'The amount of resources allocated to each component',
                    'C': 'The complexity of the system architecture',
                    'D': 'The historical performance of the system in past scenarios'
                },
                'points': 1
            },
            {
                'question_number': 2,
                'question_text': 'How does the interaction between feedback mechanisms and system performance impact overall outcomes?',
                'question_type': 'multiple_choice',
                'options': {
                    'A': 'Feedback mechanisms help to adjust actions based on performance results',
                    'B': 'Feedback mechanisms only serve to complicate the decision-making process',
                    'C': 'Feedback is irrelevant if the initial goals are clear and well-defined',
                    'D': 'Feedback mechanisms primarily slow down the response time of a system'
                },
                'points': 1
            },
            {
                'question_number': 3,
                'question_text': 'In evaluating the effectiveness of a strategy, which aspect is most critical to consider?',
                'question_type': 'multiple_choice',
                'options': {
                    'A': 'The adaptability of the strategy to changing conditions',
                    'B': 'The popularity of the strategy among peers',
                    'C': 'The initial cost involved in implementing the strategy',
                    'D': 'The duration for which the strategy has been in use'
                },
                'points': 1
            },
            {
                'question_number': 4,
                'question_text': 'What distinguishes a successful innovation from a mere idea?',
                'question_type': 'multiple_choice',
                'options': {
                    'A': 'The ability to implement the idea effectively in practice',
                    'B': 'The novelty of the idea compared to existing solutions',
                    'C': 'The level of funding secured for the idea\'s development',
                    'D': 'The recognition received from industry experts'
                },
                'points': 1
            }
        ]
